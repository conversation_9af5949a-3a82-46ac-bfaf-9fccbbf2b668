{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "HvhgbwCgZ5HolTkgBqTTK4bgm0GpoSkOLCwrPaqcPCs=", "__NEXT_PREVIEW_MODE_ID": "2fccee963520075705df29269bd28ece", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "67fccde09311656964dac3357a6936779d5bd6997be07cb7fe3af06c60340e73", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4dde5da20687e6fbe33e4897da4c20d5e6b8ad8e2dfba3fb11d062101d83f7fb"}}}, "instrumentation": null, "functions": {}}