{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "HvhgbwCgZ5HolTkgBqTTK4bgm0GpoSkOLCwrPaqcPCs=", "__NEXT_PREVIEW_MODE_ID": "ab7e874a75dae640d80e39a0be145f50", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "84b8f3b2fef107a67357dac7a66a225746f75c4d51491b1583aafdd8e1fe7c81", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3dbdc80d1fc83f720642e60dd8b9939d9e73d12f57104b3cd879a1e1174e5df2"}}}, "instrumentation": null, "functions": {}}