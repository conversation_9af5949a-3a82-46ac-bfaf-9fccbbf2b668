{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "HvhgbwCgZ5HolTkgBqTTK4bgm0GpoSkOLCwrPaqcPCs=", "__NEXT_PREVIEW_MODE_ID": "21c9e1c551c9ac3d3565165037234338", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "42cf9ffa267e2fc7b83878e3d1888c9fb57add14d89dc8e46170d5b28b8eb4cb", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "966798c42b1bb930b0ecebc9c18435191d2de1c3ca8d173465d05935d65b6f92"}}}, "instrumentation": null, "functions": {}}