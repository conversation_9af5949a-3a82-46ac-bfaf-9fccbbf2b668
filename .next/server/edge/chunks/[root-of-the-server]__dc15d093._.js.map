{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { NextResponse, type NextRequest } from 'next/server'\n\nexport async function middleware(request: NextRequest) {\n  let supabaseResponse = NextResponse.next({\n    request,\n  })\n\n  const supabase = createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return request.cookies.getAll()\n        },\n        setAll(cookiesToSet) {\n          cookiesToSet.forEach(({ name, value, options }) => request.cookies.set(name, value))\n          supabaseResponse = NextResponse.next({\n            request,\n          })\n          cookiesToSet.forEach(({ name, value, options }) =>\n            supabaseResponse.cookies.set(name, value, options)\n          )\n        },\n      },\n    }\n  )\n\n  // Refresh session if expired - required for Server Components\n  const {\n    data: { user },\n  } = await supabase.auth.getUser()\n\n  // Protected routes\n  const protectedPaths = ['/dashboard', '/trip', '/profile']\n  const isProtectedPath = protectedPaths.some(path => \n    request.nextUrl.pathname.startsWith(path)\n  )\n\n  // Auth routes\n  const authPaths = ['/auth/login', '/auth/signup']\n  const isAuthPath = authPaths.some(path => \n    request.nextUrl.pathname.startsWith(path)\n  )\n\n  // Redirect logic\n  if (isProtectedPath && !user) {\n    // Redirect to login if trying to access protected route without auth\n    const redirectUrl = new URL('/auth/login', request.url)\n    redirectUrl.searchParams.set('redirectTo', request.nextUrl.pathname)\n    return NextResponse.redirect(redirectUrl)\n  }\n\n  if (isAuthPath && user) {\n    // Redirect to dashboard if already authenticated and trying to access auth pages\n    return NextResponse.redirect(new URL('/dashboard', request.url))\n  }\n\n  return supabaseResponse\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */\n    '/((?!_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AAAA;;;AAEO,eAAe,WAAW,OAAoB;IACnD,IAAI,mBAAmB,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvC;IACF;IAEA,MAAM,WAAW,CAAA,GAAA,iLAAA,CAAA,qBAAkB,AAAD,iIAGhC;QACE,SAAS;YACP;gBACE,OAAO,QAAQ,OAAO,CAAC,MAAM;YAC/B;YACA,QAAO,YAAY;gBACjB,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAAK,QAAQ,OAAO,CAAC,GAAG,CAAC,MAAM;gBAC7E,mBAAmB,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACnC;gBACF;gBACA,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,iBAAiB,OAAO,CAAC,GAAG,CAAC,MAAM,OAAO;YAE9C;QACF;IACF;IAGF,8DAA8D;IAC9D,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,mBAAmB;IACnB,MAAM,iBAAiB;QAAC;QAAc;QAAS;KAAW;IAC1D,MAAM,kBAAkB,eAAe,IAAI,CAAC,CAAA,OAC1C,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;IAGtC,cAAc;IACd,MAAM,YAAY;QAAC;QAAe;KAAe;IACjD,MAAM,aAAa,UAAU,IAAI,CAAC,CAAA,OAChC,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;IAGtC,iBAAiB;IACjB,IAAI,mBAAmB,CAAC,MAAM;QAC5B,qEAAqE;QACrE,MAAM,cAAc,IAAI,IAAI,eAAe,QAAQ,GAAG;QACtD,YAAY,YAAY,CAAC,GAAG,CAAC,cAAc,QAAQ,OAAO,CAAC,QAAQ;QACnE,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,IAAI,cAAc,MAAM;QACtB,iFAAiF;QACjF,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,QAAQ,GAAG;IAChE;IAEA,OAAO;AACT;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}