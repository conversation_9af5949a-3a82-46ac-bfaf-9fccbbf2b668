module.exports = {

"[project]/node_modules/@react-email/render/dist/node/index.mjs [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/node_modules_next_dist_compiled_react-dom_server_3853c32a.js",
  "server/chunks/node_modules_fb4c5661._.js",
  "server/chunks/[root-of-the-server]__02325f31._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@react-email/render/dist/node/index.mjs [app-route] (ecmascript)");
    });
});
}}),

};