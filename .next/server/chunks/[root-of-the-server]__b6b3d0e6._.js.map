{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/lib/supabase-server.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\nimport { Database } from './database.types'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Server-side Supabase client\nexport const createServerComponentClient = async () => {\n  const cookieStore = await cookies()\n  \n  return createServerClient<Database>(\n    supabaseUrl,\n    supabaseAnonKey,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n\n// Admin client for server-side operations\nexport const createAdminClient = () =>\n  createClient<Database>(\n    supabaseUrl,\n    process.env.SUPABASE_SERVICE_ROLE_KEY!,\n    {\n      auth: {\n        autoRefreshToken: false,\n        persistSession: false\n      }\n    }\n  )\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;;;;AAGA,MAAM;AACN,MAAM;AAGC,MAAM,8BAA8B;IACzC,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EACtB,aACA,iBACA;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ;AAGO,MAAM,oBAAoB,IAC/B,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EACT,aACA,QAAQ,GAAG,CAAC,yBAAyB,EACrC;QACE,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;QAClB;IACF", "debugId": null}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/app/api/trips/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createServerComponentClient } from '@/lib/supabase-server'\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const supabase = await createServerComponentClient()\n\n    // Get the current user\n    const { data: { user }, error: authError } = await supabase.auth.getUser()\n\n    if (authError || !user) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const { id } = await params\n\n    // Get the trip\n    const { data: trip, error: tripError } = await supabase\n      .from('trips')\n      .select('*')\n      .eq('id', id)\n      .single()\n\n    if (tripError || !trip) {\n      return NextResponse.json({ error: 'Trip not found' }, { status: 404 })\n    }\n\n    // Check if user has access to this trip (owner or invited)\n    const isOwner = trip.user_id === user.id\n    let hasAccess = isOwner\n\n    if (!isOwner) {\n      const { data: invitation } = await supabase\n        .from('trip_invitations')\n        .select('id')\n        .eq('trip_id', id)\n        .eq('invited_user_id', user.id)\n        .eq('status', 'accepted')\n        .single()\n\n      hasAccess = !!invitation\n    }\n\n    if (!hasAccess) {\n      return NextResponse.json({ \n        error: 'You do not have access to this trip' \n      }, { status: 403 })\n    }\n\n    return NextResponse.json({ trip })\n  } catch (error) {\n    console.error('Error in GET /api/trips/[id]:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n\nexport async function PUT(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const supabase = await createServerComponentClient()\n\n    // Get the current user\n    const { data: { user }, error: authError } = await supabase.auth.getUser()\n\n    if (authError || !user) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const { id } = await params\n    const body = await request.json()\n\n    // Verify the trip belongs to the user\n    const { data: trip, error: tripError } = await supabase\n      .from('trips')\n      .select('*')\n      .eq('id', id)\n      .eq('user_id', user.id)\n      .single()\n\n    if (tripError || !trip) {\n      return NextResponse.json({ error: 'Trip not found' }, { status: 404 })\n    }\n\n    // Update the trip\n    const { data: updatedTrip, error: updateError } = await supabase\n      .from('trips')\n      .update(body)\n      .eq('id', id)\n      .select()\n      .single()\n\n    if (updateError) {\n      console.error('Error updating trip:', updateError)\n      return NextResponse.json({ error: 'Failed to update trip' }, { status: 500 })\n    }\n\n    return NextResponse.json({ trip: updatedTrip })\n  } catch (error) {\n    console.error('Error in PUT /api/trips/[id]:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n\nexport async function DELETE(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const supabase = await createServerComponentClient()\n\n    // Get the current user\n    const { data: { user }, error: authError } = await supabase.auth.getUser()\n\n    if (authError || !user) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const { id } = await params\n\n    // Verify the trip belongs to the user\n    const { data: trip, error: tripError } = await supabase\n      .from('trips')\n      .select('*')\n      .eq('id', id)\n      .eq('user_id', user.id)\n      .single()\n\n    if (tripError || !trip) {\n      return NextResponse.json({ error: 'Trip not found' }, { status: 404 })\n    }\n\n    // Delete the trip (cascading deletes will handle related records)\n    const { error: deleteError } = await supabase\n      .from('trips')\n      .delete()\n      .eq('id', id)\n\n    if (deleteError) {\n      console.error('Error deleting trip:', deleteError)\n      return NextResponse.json({ error: 'Failed to delete trip' }, { status: 500 })\n    }\n\n    return NextResponse.json({ message: 'Trip deleted successfully' })\n  } catch (error) {\n    console.error('Error in DELETE /api/trips/[id]:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,8BAA2B,AAAD;QAEjD,uBAAuB;QACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QAErB,eAAe;QACf,MAAM,EAAE,MAAM,IAAI,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAC5C,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAiB,GAAG;gBAAE,QAAQ;YAAI;QACtE;QAEA,2DAA2D;QAC3D,MAAM,UAAU,KAAK,OAAO,KAAK,KAAK,EAAE;QACxC,IAAI,YAAY;QAEhB,IAAI,CAAC,SAAS;YACZ,MAAM,EAAE,MAAM,UAAU,EAAE,GAAG,MAAM,SAChC,IAAI,CAAC,oBACL,MAAM,CAAC,MACP,EAAE,CAAC,WAAW,IACd,EAAE,CAAC,mBAAmB,KAAK,EAAE,EAC7B,EAAE,CAAC,UAAU,YACb,MAAM;YAET,YAAY,CAAC,CAAC;QAChB;QAEA,IAAI,CAAC,WAAW;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAK;IAClC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF;AAEO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,8BAA2B,AAAD;QAEjD,uBAAuB;QACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,sCAAsC;QACtC,MAAM,EAAE,MAAM,IAAI,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAC5C,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,MAAM;QAET,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAiB,GAAG;gBAAE,QAAQ;YAAI;QACtE;QAEA,kBAAkB;QAClB,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SACrD,IAAI,CAAC,SACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAwB,GAAG;gBAAE,QAAQ;YAAI;QAC7E;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,MAAM;QAAY;IAC/C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF;AAEO,eAAe,OACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,8BAA2B,AAAD;QAEjD,uBAAuB;QACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QAErB,sCAAsC;QACtC,MAAM,EAAE,MAAM,IAAI,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAC5C,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,MAAM;QAET,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAiB,GAAG;gBAAE,QAAQ;YAAI;QACtE;QAEA,kEAAkE;QAClE,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,SACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAwB,GAAG;gBAAE,QAAQ;YAAI;QAC7E;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAA4B;IAClE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}