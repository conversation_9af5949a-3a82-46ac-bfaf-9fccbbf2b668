{"version": 3, "sources": [], "sections": [{"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient } from '@supabase/ssr'\nimport { Database } from './database.types'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Client-side Supabase client\nexport const createClientComponentClient = () =>\n  createBrowserClient<Database>(supabaseUrl, supabaseAnonKey)\n\n// Simple client for basic operations\nexport const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey)\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;AAGA,MAAM;AACN,MAAM;AAGC,MAAM,8BAA8B,IACzC,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD,EAAY,aAAa;AAGtC,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAY,aAAa", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/hooks/useAuth.ts"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { User } from '@supabase/supabase-js'\nimport { createClientComponentClient } from '@/lib/supabase'\nimport { useRouter } from 'next/navigation'\n\nexport function useAuth() {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n  const supabase = createClientComponentClient()\n  const router = useRouter()\n\n  useEffect(() => {\n    const getUser = async () => {\n      const { data: { user } } = await supabase.auth.getUser()\n      setUser(user)\n      setLoading(false)\n    }\n\n    getUser()\n\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        setUser(session?.user ?? null)\n        setLoading(false)\n        \n        if (event === 'SIGNED_IN') {\n          router.push('/dashboard')\n        } else if (event === 'SIGNED_OUT') {\n          router.push('/')\n        }\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [supabase, router])\n\n  const signUp = async (email: string, password: string, fullName: string) => {\n    const { data, error } = await supabase.auth.signUp({\n      email,\n      password,\n      options: {\n        data: {\n          full_name: fullName,\n        },\n      },\n    })\n\n    if (error) throw error\n    return data\n  }\n\n  const signIn = async (email: string, password: string) => {\n    const { data, error } = await supabase.auth.signInWithPassword({\n      email,\n      password,\n    })\n\n    if (error) throw error\n    return data\n  }\n\n  const signOut = async () => {\n    const { error } = await supabase.auth.signOut()\n    if (error) throw error\n  }\n\n  const resetPassword = async (email: string) => {\n    const { error } = await supabase.auth.resetPasswordForEmail(email, {\n      redirectTo: `${window.location.origin}/auth/reset-password`,\n    })\n\n    if (error) throw error\n  }\n\n  return {\n    user,\n    loading,\n    signUp,\n    signIn,\n    signOut,\n    resetPassword,\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AAEA;AACA;AALA;;;;AAOO,SAAS;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,8BAA2B,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU;YACd,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,QAAQ;YACR,WAAW;QACb;QAEA;QAEA,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB,CAChE,OAAO,OAAO;YACZ,QAAQ,SAAS,QAAQ;YACzB,WAAW;YAEX,IAAI,UAAU,aAAa;gBACzB,OAAO,IAAI,CAAC;YACd,OAAO,IAAI,UAAU,cAAc;gBACjC,OAAO,IAAI,CAAC;YACd;QACF;QAGF,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG;QAAC;QAAU;KAAO;IAErB,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;YACjD;YACA;YACA,SAAS;gBACP,MAAM;oBACJ,WAAW;gBACb;YACF;QACF;QAEA,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;YAC7D;YACA;QACF;QAEA,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,UAAU;QACd,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAC7C,IAAI,OAAO,MAAM;IACnB;IAEA,MAAM,gBAAgB,OAAO;QAC3B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,qBAAqB,CAAC,OAAO;YACjE,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,oBAAoB,CAAC;QAC7D;QAEA,IAAI,OAAO,MAAM;IACnB;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: string | Date): string {\n  return new Date(date).toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  })\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(amount)\n}\n\nexport function calculateTripDuration(startDate: string, endDate: string): number {\n  const start = new Date(startDate)\n  const end = new Date(endDate)\n  const diffTime = Math.abs(end.getTime() - start.getTime())\n  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))\n  return diffDays\n}\n\nexport function generateTripId(): string {\n  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w ]+/g, '')\n    .replace(/ +/g, '-')\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;QAChD,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,sBAAsB,SAAiB,EAAE,OAAe;IACtE,MAAM,QAAQ,IAAI,KAAK;IACvB,MAAM,MAAM,IAAI,KAAK;IACrB,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,OAAO,KAAK,MAAM,OAAO;IACvD,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IAC1D,OAAO;AACT;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;AAC/F;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,OAAO;AACpB", "debugId": null}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-blue-600 text-white hover:bg-blue-700\",\n        destructive:\n          \"bg-red-600 text-white hover:bg-red-700\",\n        outline:\n          \"border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900\",\n        secondary:\n          \"bg-gray-100 text-gray-900 hover:bg-gray-200\",\n        ghost: \"hover:bg-gray-100 hover:text-gray-900\",\n        link: \"text-blue-600 underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,uQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-white text-gray-900 shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-gray-600\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 421, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/app/trip/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport Link from 'next/link'\nimport { useAuth } from '@/hooks/useAuth'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { MapPin, ArrowLeft, Calendar, DollarSign, Users, Mail, CheckCircle, Clock, X } from 'lucide-react'\nimport { formatDate, formatCurrency } from '@/lib/utils'\n\ninterface Trip {\n  id: string\n  title: string\n  description: string\n  destination: string\n  start_date: string\n  end_date: string\n  budget: number\n  preferences: any\n  itinerary: any\n  created_at: string\n}\n\ninterface Invitation {\n  id: string\n  email: string\n  status: 'pending' | 'accepted' | 'declined'\n  created_at: string\n  invited_user?: {\n    full_name: string\n  }\n}\n\nexport default function TripPage({ params }: { params: { id: string } }) {\n  const { user, loading: authLoading } = useAuth()\n  const [trip, setTrip] = useState<Trip | null>(null)\n  const [invitations, setInvitations] = useState<Invitation[]>([])\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState('')\n\n  useEffect(() => {\n    if (user) {\n      fetchTripData()\n    }\n  }, [user, params.id])\n\n  const fetchTripData = async () => {\n    try {\n      // Fetch trip details\n      const tripResponse = await fetch(`/api/trips/${params.id}`)\n      const tripData = await tripResponse.json()\n\n      if (!tripResponse.ok) {\n        setError(tripData.error || 'Failed to load trip')\n        return\n      }\n\n      setTrip(tripData.trip)\n\n      // Fetch invitations\n      const invitationsResponse = await fetch(`/api/invitations?tripId=${params.id}`)\n      const invitationsData = await invitationsResponse.json()\n\n      if (invitationsResponse.ok) {\n        setInvitations(invitationsData.invitations || [])\n      }\n\n    } catch (err) {\n      setError('Failed to load trip')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (authLoading || loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading trip...</p>\n        </div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <Card>\n          <CardHeader>\n            <CardTitle>Authentication Required</CardTitle>\n            <CardDescription>Please sign in to view this trip.</CardDescription>\n          </CardHeader>\n          <CardContent>\n            <Link href=\"/auth/login\">\n              <Button>Sign In</Button>\n            </Link>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center p-4\">\n        <Card className=\"w-full max-w-md\">\n          <CardHeader className=\"text-center\">\n            <CardTitle>Error</CardTitle>\n            <CardDescription>{error}</CardDescription>\n          </CardHeader>\n          <CardContent className=\"text-center\">\n            <Link href=\"/dashboard\">\n              <Button>Go to Dashboard</Button>\n            </Link>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  if (!trip) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <p className=\"text-gray-600\">Trip not found</p>\n        </div>\n      </div>\n    )\n  }\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'accepted':\n        return <CheckCircle className=\"h-4 w-4 text-green-500\" />\n      case 'declined':\n        return <X className=\"h-4 w-4 text-red-500\" />\n      default:\n        return <Clock className=\"h-4 w-4 text-yellow-500\" />\n    }\n  }\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'accepted':\n        return 'Accepted'\n      case 'declined':\n        return 'Declined'\n      default:\n        return 'Pending'\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex items-center space-x-4\">\n            <Link href=\"/dashboard\">\n              <Button variant=\"ghost\" size=\"sm\">\n                <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                Back to Dashboard\n              </Button>\n            </Link>\n            <div className=\"flex items-center space-x-2\">\n              <MapPin className=\"h-6 w-6 text-blue-600\" />\n              <span className=\"text-xl font-bold text-gray-900\">Trip Details</span>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"container mx-auto px-4 py-8 max-w-4xl\">\n        {/* Trip Overview */}\n        <Card className=\"mb-8\">\n          <CardHeader>\n            <CardTitle className=\"text-2xl\">{trip.title}</CardTitle>\n            <CardDescription>\n              {trip.description}\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid md:grid-cols-3 gap-4\">\n              <div className=\"flex items-center\">\n                <MapPin className=\"h-5 w-5 mr-2 text-gray-500\" />\n                <span>{trip.destination}</span>\n              </div>\n              <div className=\"flex items-center\">\n                <Calendar className=\"h-5 w-5 mr-2 text-gray-500\" />\n                <span>{formatDate(trip.start_date)} - {formatDate(trip.end_date)}</span>\n              </div>\n              {trip.budget && (\n                <div className=\"flex items-center\">\n                  <DollarSign className=\"h-5 w-5 mr-2 text-gray-500\" />\n                  <span>{formatCurrency(trip.budget)}</span>\n                </div>\n              )}\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Invitations */}\n        {invitations.length > 0 && (\n          <Card className=\"mb-8\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <Users className=\"h-5 w-5 mr-2\" />\n                Travel Companions ({invitations.length})\n              </CardTitle>\n              <CardDescription>\n                People you've invited to join this trip\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3\">\n                {invitations.map((invitation) => (\n                  <div key={invitation.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                    <div className=\"flex items-center space-x-3\">\n                      <Mail className=\"h-4 w-4 text-gray-500\" />\n                      <div>\n                        <p className=\"font-medium\">\n                          {invitation.invited_user?.full_name || invitation.email}\n                        </p>\n                        {invitation.invited_user?.full_name && (\n                          <p className=\"text-sm text-gray-500\">{invitation.email}</p>\n                        )}\n                      </div>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      {getStatusIcon(invitation.status)}\n                      <span className=\"text-sm font-medium\">\n                        {getStatusText(invitation.status)}\n                      </span>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        )}\n\n        {/* Trip Preferences */}\n        {trip.preferences && (\n          <Card className=\"mb-8\">\n            <CardHeader>\n              <CardTitle>Trip Preferences</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid md:grid-cols-2 gap-6\">\n                {trip.preferences.interests && trip.preferences.interests.length > 0 && (\n                  <div>\n                    <h4 className=\"font-semibold mb-2\">Interests</h4>\n                    <div className=\"flex flex-wrap gap-2\">\n                      {trip.preferences.interests.map((interest: string) => (\n                        <span\n                          key={interest}\n                          className=\"px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded-md\"\n                        >\n                          {interest}\n                        </span>\n                      ))}\n                    </div>\n                  </div>\n                )}\n                <div>\n                  <h4 className=\"font-semibold mb-2\">Trip Style</h4>\n                  <div className=\"space-y-1 text-sm\">\n                    <p><span className=\"font-medium\">Budget Level:</span> {trip.preferences.budget_level}</p>\n                    <p><span className=\"font-medium\">Pace:</span> {trip.preferences.pace}</p>\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        )}\n\n        {/* Itinerary */}\n        {trip.itinerary ? (\n          <Card>\n            <CardHeader>\n              <CardTitle>Itinerary</CardTitle>\n              <CardDescription>\n                AI-generated itinerary based on your preferences\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-gray-600\">Itinerary details will be displayed here...</p>\n              {/* TODO: Add itinerary display component */}\n            </CardContent>\n          </Card>\n        ) : (\n          <Card>\n            <CardHeader>\n              <CardTitle>Itinerary</CardTitle>\n              <CardDescription>\n                {invitations.length > 0 \n                  ? \"Waiting for all invitees to vote on preferences before generating itinerary\"\n                  : \"Ready to generate your personalized itinerary\"\n                }\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              {invitations.length === 0 && (\n                <Button>\n                  Generate Itinerary\n                </Button>\n              )}\n              {invitations.length > 0 && (\n                <div className=\"text-center py-8\">\n                  <Clock className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                  <p className=\"text-gray-600\">\n                    Once all invited travelers have voted on their preferences, \n                    the AI will generate a personalized itinerary for your group.\n                  </p>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        )}\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AARA;;;;;;;;;AAiCe,SAAS,SAAS,EAAE,MAAM,EAA8B;IACrE,MAAM,EAAE,IAAI,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR;QACF;IACF,GAAG;QAAC;QAAM,OAAO,EAAE;KAAC;IAEpB,MAAM,gBAAgB;QACpB,IAAI;YACF,qBAAqB;YACrB,MAAM,eAAe,MAAM,MAAM,CAAC,WAAW,EAAE,OAAO,EAAE,EAAE;YAC1D,MAAM,WAAW,MAAM,aAAa,IAAI;YAExC,IAAI,CAAC,aAAa,EAAE,EAAE;gBACpB,SAAS,SAAS,KAAK,IAAI;gBAC3B;YACF;YAEA,QAAQ,SAAS,IAAI;YAErB,oBAAoB;YACpB,MAAM,sBAAsB,MAAM,MAAM,CAAC,wBAAwB,EAAE,OAAO,EAAE,EAAE;YAC9E,MAAM,kBAAkB,MAAM,oBAAoB,IAAI;YAEtD,IAAI,oBAAoB,EAAE,EAAE;gBAC1B,eAAe,gBAAgB,WAAW,IAAI,EAAE;YAClD;QAEF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,eAAe,SAAS;QAC1B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAEnB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;0CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMpB;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAE;;;;;;;;;;;;kCAEpB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;0CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMpB;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;IAIrC;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,4LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;;YACtB;gBACE,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;;sDAC3B,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAI1C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO1D,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAY,KAAK,KAAK;;;;;;kDAC3C,8OAAC,gIAAA,CAAA,kBAAe;kDACb,KAAK,WAAW;;;;;;;;;;;;0CAGrB,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;8DAAM,KAAK,WAAW;;;;;;;;;;;;sDAEzB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;;wDAAM,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,UAAU;wDAAE;wDAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,QAAQ;;;;;;;;;;;;;wCAEhE,KAAK,MAAM,kBACV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC;8DAAM,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQ1C,YAAY,MAAM,GAAG,mBACpB,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;4CACd,YAAY,MAAM;4CAAC;;;;;;;kDAEzC,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,2BAChB,8OAAC;4CAAwB,WAAU;;8DACjC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EACV,WAAW,YAAY,EAAE,aAAa,WAAW,KAAK;;;;;;gEAExD,WAAW,YAAY,EAAE,2BACxB,8OAAC;oEAAE,WAAU;8EAAyB,WAAW,KAAK;;;;;;;;;;;;;;;;;;8DAI5D,8OAAC;oDAAI,WAAU;;wDACZ,cAAc,WAAW,MAAM;sEAChC,8OAAC;4DAAK,WAAU;sEACb,cAAc,WAAW,MAAM;;;;;;;;;;;;;2CAf5B,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;oBA0BhC,KAAK,WAAW,kBACf,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;;;;;;0CAEb,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;wCACZ,KAAK,WAAW,CAAC,SAAS,IAAI,KAAK,WAAW,CAAC,SAAS,CAAC,MAAM,GAAG,mBACjE,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,8OAAC;oDAAI,WAAU;8DACZ,KAAK,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,yBAC/B,8OAAC;4DAEC,WAAU;sEAET;2DAHI;;;;;;;;;;;;;;;;sDASf,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EAAE,8OAAC;oEAAK,WAAU;8EAAc;;;;;;gEAAoB;gEAAE,KAAK,WAAW,CAAC,YAAY;;;;;;;sEACpF,8OAAC;;8EAAE,8OAAC;oEAAK,WAAU;8EAAc;;;;;;gEAAY;gEAAE,KAAK,WAAW,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAS/E,KAAK,SAAS,iBACb,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;6CAKjC,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;kDACb,YAAY,MAAM,GAAG,IAClB,gFACA;;;;;;;;;;;;0CAIR,8OAAC,gIAAA,CAAA,cAAW;;oCACT,YAAY,MAAM,KAAK,mBACtB,8OAAC,kIAAA,CAAA,SAAM;kDAAC;;;;;;oCAIT,YAAY,MAAM,GAAG,mBACpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY/C", "debugId": null}}]}