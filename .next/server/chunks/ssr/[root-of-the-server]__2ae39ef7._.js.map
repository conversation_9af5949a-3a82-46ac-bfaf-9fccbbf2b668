{"version": 3, "sources": [], "sections": [{"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient } from '@supabase/ssr'\nimport { Database } from './database.types'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Client-side Supabase client\nexport const createClientComponentClient = () =>\n  createBrowserClient<Database>(supabaseUrl, supabaseAnonKey)\n\n// Simple client for basic operations\nexport const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey)\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;AAGA,MAAM;AACN,MAAM;AAGC,MAAM,8BAA8B,IACzC,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD,EAAY,aAAa;AAGtC,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAY,aAAa", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/hooks/useAuth.ts"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { User } from '@supabase/supabase-js'\nimport { createClientComponentClient } from '@/lib/supabase'\nimport { useRouter } from 'next/navigation'\n\nexport function useAuth() {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n  const supabase = createClientComponentClient()\n  const router = useRouter()\n\n  useEffect(() => {\n    const getUser = async () => {\n      const { data: { user } } = await supabase.auth.getUser()\n      setUser(user)\n      setLoading(false)\n    }\n\n    getUser()\n\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        setUser(session?.user ?? null)\n        setLoading(false)\n        \n        if (event === 'SIGNED_IN') {\n          router.push('/dashboard')\n        } else if (event === 'SIGNED_OUT') {\n          router.push('/')\n        }\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [supabase, router])\n\n  const signUp = async (email: string, password: string, fullName: string) => {\n    const { data, error } = await supabase.auth.signUp({\n      email,\n      password,\n      options: {\n        data: {\n          full_name: fullName,\n        },\n      },\n    })\n\n    if (error) throw error\n    return data\n  }\n\n  const signIn = async (email: string, password: string) => {\n    const { data, error } = await supabase.auth.signInWithPassword({\n      email,\n      password,\n    })\n\n    if (error) throw error\n    return data\n  }\n\n  const signOut = async () => {\n    const { error } = await supabase.auth.signOut()\n    if (error) throw error\n  }\n\n  const resetPassword = async (email: string) => {\n    const { error } = await supabase.auth.resetPasswordForEmail(email, {\n      redirectTo: `${window.location.origin}/auth/reset-password`,\n    })\n\n    if (error) throw error\n  }\n\n  return {\n    user,\n    loading,\n    signUp,\n    signIn,\n    signOut,\n    resetPassword,\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AAEA;AACA;AALA;;;;AAOO,SAAS;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,8BAA2B,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU;YACd,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,QAAQ;YACR,WAAW;QACb;QAEA;QAEA,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB,CAChE,OAAO,OAAO;YACZ,QAAQ,SAAS,QAAQ;YACzB,WAAW;YAEX,IAAI,UAAU,aAAa;gBACzB,OAAO,IAAI,CAAC;YACd,OAAO,IAAI,UAAU,cAAc;gBACjC,OAAO,IAAI,CAAC;YACd;QACF;QAGF,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG;QAAC;QAAU;KAAO;IAErB,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;YACjD;YACA;YACA,SAAS;gBACP,MAAM;oBACJ,WAAW;gBACb;YACF;QACF;QAEA,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;YAC7D;YACA;QACF;QAEA,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,UAAU;QACd,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAC7C,IAAI,OAAO,MAAM;IACnB;IAEA,MAAM,gBAAgB,OAAO;QAC3B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,qBAAqB,CAAC,OAAO;YACjE,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,oBAAoB,CAAC;QAC7D;QAEA,IAAI,OAAO,MAAM;IACnB;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: string | Date): string {\n  return new Date(date).toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  })\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(amount)\n}\n\nexport function calculateTripDuration(startDate: string, endDate: string): number {\n  const start = new Date(startDate)\n  const end = new Date(endDate)\n  const diffTime = Math.abs(end.getTime() - start.getTime())\n  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))\n  return diffDays\n}\n\nexport function generateTripId(): string {\n  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w ]+/g, '')\n    .replace(/ +/g, '-')\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;QAChD,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,sBAAsB,SAAiB,EAAE,OAAe;IACtE,MAAM,QAAQ,IAAI,KAAK;IACvB,MAAM,MAAM,IAAI,KAAK;IACrB,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,OAAO,KAAK,MAAM,OAAO;IACvD,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IAC1D,OAAO;AACT;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;AAC/F;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,OAAO;AACpB", "debugId": null}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 369, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 450, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/app/trip/create/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { useAuth } from '@/hooks/useAuth'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { MapPin, ArrowLeft, Calendar, DollarSign, Sparkles } from 'lucide-react'\n\nexport default function CreateTripPage() {\n  const { user } = useAuth()\n  const router = useRouter()\n  \n  const [formData, setFormData] = useState({\n    title: '',\n    description: '',\n    destination: '',\n    start_date: '',\n    end_date: '',\n    budget: '',\n    interests: [] as string[],\n    budget_level: 'mid-range' as 'budget' | 'mid-range' | 'luxury',\n    pace: 'moderate' as 'relaxed' | 'moderate' | 'packed',\n  })\n  \n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value } = e.target\n    setFormData(prev => ({ ...prev, [name]: value }))\n  }\n\n  const handleInterestToggle = (interest: string) => {\n    setFormData(prev => ({\n      ...prev,\n      interests: prev.interests.includes(interest)\n        ? prev.interests.filter(i => i !== interest)\n        : [...prev.interests, interest]\n    }))\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError('')\n\n    try {\n      // Create the trip\n      const tripResponse = await fetch('/api/trips', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          title: formData.title,\n          description: formData.description,\n          destination: formData.destination,\n          start_date: formData.start_date,\n          end_date: formData.end_date,\n          budget: formData.budget ? parseFloat(formData.budget) : null,\n          preferences: {\n            interests: formData.interests,\n            budget_level: formData.budget_level,\n            pace: formData.pace,\n          },\n        }),\n      })\n\n      if (!tripResponse.ok) {\n        throw new Error('Failed to create trip')\n      }\n\n      const { trip } = await tripResponse.json()\n\n      // Generate itinerary\n      const itineraryResponse = await fetch('/api/ai/generate-itinerary', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          tripId: trip.id,\n          destination: formData.destination,\n          startDate: formData.start_date,\n          endDate: formData.end_date,\n          preferences: {\n            interests: formData.interests,\n            budget_level: formData.budget_level,\n            pace: formData.pace,\n          },\n          budget: formData.budget ? parseFloat(formData.budget) : null,\n        }),\n      })\n\n      if (!itineraryResponse.ok) {\n        console.error('Failed to generate itinerary, but trip was created')\n      }\n\n      router.push(`/trip/${trip.id}`)\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while creating your trip')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const interestOptions = [\n    'Culture & History', 'Food & Dining', 'Adventure & Outdoor', 'Art & Museums',\n    'Nightlife & Entertainment', 'Shopping', 'Nature & Wildlife', 'Architecture',\n    'Photography', 'Local Experiences', 'Relaxation & Wellness', 'Sports & Activities'\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex items-center space-x-4\">\n            <Link href=\"/dashboard\">\n              <Button variant=\"ghost\" size=\"sm\">\n                <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                Back to Dashboard\n              </Button>\n            </Link>\n            <div className=\"flex items-center space-x-2\">\n              <MapPin className=\"h-6 w-6 text-blue-600\" />\n              <span className=\"text-xl font-bold text-gray-900\">Create New Trip</span>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"container mx-auto px-4 py-8 max-w-2xl\">\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center\">\n              <Sparkles className=\"h-6 w-6 mr-2 text-blue-600\" />\n              Plan Your Adventure\n            </CardTitle>\n            <CardDescription>\n              Tell us about your trip and we'll create a personalized itinerary for you\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              {error && (\n                <div className=\"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm\">\n                  {error}\n                </div>\n              )}\n\n              {/* Basic Information */}\n              <div className=\"space-y-4\">\n                <h3 className=\"text-lg font-semibold\">Basic Information</h3>\n                \n                <div className=\"space-y-2\">\n                  <label htmlFor=\"title\" className=\"text-sm font-medium text-gray-700\">\n                    Trip Title *\n                  </label>\n                  <Input\n                    id=\"title\"\n                    name=\"title\"\n                    placeholder=\"e.g., Summer Adventure in Japan\"\n                    value={formData.title}\n                    onChange={handleInputChange}\n                    required\n                  />\n                </div>\n\n                <div className=\"space-y-2\">\n                  <label htmlFor=\"destination\" className=\"text-sm font-medium text-gray-700\">\n                    Destination *\n                  </label>\n                  <Input\n                    id=\"destination\"\n                    name=\"destination\"\n                    placeholder=\"e.g., Tokyo, Japan\"\n                    value={formData.destination}\n                    onChange={handleInputChange}\n                    required\n                  />\n                </div>\n\n                <div className=\"space-y-2\">\n                  <label htmlFor=\"description\" className=\"text-sm font-medium text-gray-700\">\n                    Description\n                  </label>\n                  <textarea\n                    id=\"description\"\n                    name=\"description\"\n                    rows={3}\n                    className=\"flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\"\n                    placeholder=\"Tell us more about your trip...\"\n                    value={formData.description}\n                    onChange={handleInputChange}\n                  />\n                </div>\n              </div>\n\n              {/* Dates and Budget */}\n              <div className=\"space-y-4\">\n                <h3 className=\"text-lg font-semibold\">Dates & Budget</h3>\n                \n                <div className=\"grid md:grid-cols-2 gap-4\">\n                  <div className=\"space-y-2\">\n                    <label htmlFor=\"start_date\" className=\"text-sm font-medium text-gray-700\">\n                      Start Date *\n                    </label>\n                    <Input\n                      id=\"start_date\"\n                      name=\"start_date\"\n                      type=\"date\"\n                      value={formData.start_date}\n                      onChange={handleInputChange}\n                      required\n                    />\n                  </div>\n                  \n                  <div className=\"space-y-2\">\n                    <label htmlFor=\"end_date\" className=\"text-sm font-medium text-gray-700\">\n                      End Date *\n                    </label>\n                    <Input\n                      id=\"end_date\"\n                      name=\"end_date\"\n                      type=\"date\"\n                      value={formData.end_date}\n                      onChange={handleInputChange}\n                      required\n                    />\n                  </div>\n                </div>\n\n                <div className=\"space-y-2\">\n                  <label htmlFor=\"budget\" className=\"text-sm font-medium text-gray-700\">\n                    Budget (USD)\n                  </label>\n                  <Input\n                    id=\"budget\"\n                    name=\"budget\"\n                    type=\"number\"\n                    placeholder=\"e.g., 2000\"\n                    value={formData.budget}\n                    onChange={handleInputChange}\n                  />\n                </div>\n              </div>\n\n              {/* Preferences */}\n              <div className=\"space-y-4\">\n                <h3 className=\"text-lg font-semibold\">Preferences</h3>\n                \n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium text-gray-700\">\n                    Interests (select all that apply)\n                  </label>\n                  <div className=\"grid grid-cols-2 md:grid-cols-3 gap-2\">\n                    {interestOptions.map((interest) => (\n                      <button\n                        key={interest}\n                        type=\"button\"\n                        onClick={() => handleInterestToggle(interest)}\n                        className={`text-sm px-3 py-2 rounded-md border transition-colors ${\n                          formData.interests.includes(interest)\n                            ? 'bg-blue-100 border-blue-300 text-blue-700'\n                            : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'\n                        }`}\n                      >\n                        {interest}\n                      </button>\n                    ))}\n                  </div>\n                </div>\n\n                <div className=\"grid md:grid-cols-2 gap-4\">\n                  <div className=\"space-y-2\">\n                    <label htmlFor=\"budget_level\" className=\"text-sm font-medium text-gray-700\">\n                      Budget Level\n                    </label>\n                    <select\n                      id=\"budget_level\"\n                      name=\"budget_level\"\n                      value={formData.budget_level}\n                      onChange={handleInputChange}\n                      className=\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\"\n                    >\n                      <option value=\"budget\">Budget</option>\n                      <option value=\"mid-range\">Mid-range</option>\n                      <option value=\"luxury\">Luxury</option>\n                    </select>\n                  </div>\n                  \n                  <div className=\"space-y-2\">\n                    <label htmlFor=\"pace\" className=\"text-sm font-medium text-gray-700\">\n                      Trip Pace\n                    </label>\n                    <select\n                      id=\"pace\"\n                      name=\"pace\"\n                      value={formData.pace}\n                      onChange={handleInputChange}\n                      className=\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\"\n                    >\n                      <option value=\"relaxed\">Relaxed</option>\n                      <option value=\"moderate\">Moderate</option>\n                      <option value=\"packed\">Packed</option>\n                    </select>\n                  </div>\n                </div>\n              </div>\n\n              <Button type=\"submit\" className=\"w-full\" size=\"lg\" disabled={loading}>\n                {loading ? (\n                  <>\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                    Creating Your Trip...\n                  </>\n                ) : (\n                  <>\n                    <Sparkles className=\"h-5 w-5 mr-2\" />\n                    Create Trip & Generate Itinerary\n                  </>\n                )}\n              </Button>\n            </form>\n          </CardContent>\n        </Card>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AATA;;;;;;;;;;AAWe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,aAAa;QACb,aAAa;QACb,YAAY;QACZ,UAAU;QACV,QAAQ;QACR,WAAW,EAAE;QACb,cAAc;QACd,MAAM;IACR;IAEA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;IACjD;IAEA,MAAM,uBAAuB,CAAC;QAC5B,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,WAAW,KAAK,SAAS,CAAC,QAAQ,CAAC,YAC/B,KAAK,SAAS,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM,YACjC;uBAAI,KAAK,SAAS;oBAAE;iBAAS;YACnC,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,kBAAkB;YAClB,MAAM,eAAe,MAAM,MAAM,cAAc;gBAC7C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO,SAAS,KAAK;oBACrB,aAAa,SAAS,WAAW;oBACjC,aAAa,SAAS,WAAW;oBACjC,YAAY,SAAS,UAAU;oBAC/B,UAAU,SAAS,QAAQ;oBAC3B,QAAQ,SAAS,MAAM,GAAG,WAAW,SAAS,MAAM,IAAI;oBACxD,aAAa;wBACX,WAAW,SAAS,SAAS;wBAC7B,cAAc,SAAS,YAAY;wBACnC,MAAM,SAAS,IAAI;oBACrB;gBACF;YACF;YAEA,IAAI,CAAC,aAAa,EAAE,EAAE;gBACpB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,aAAa,IAAI;YAExC,qBAAqB;YACrB,MAAM,oBAAoB,MAAM,MAAM,8BAA8B;gBAClE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ,KAAK,EAAE;oBACf,aAAa,SAAS,WAAW;oBACjC,WAAW,SAAS,UAAU;oBAC9B,SAAS,SAAS,QAAQ;oBAC1B,aAAa;wBACX,WAAW,SAAS,SAAS;wBAC7B,cAAc,SAAS,YAAY;wBACnC,MAAM,SAAS,IAAI;oBACrB;oBACA,QAAQ,SAAS,MAAM,GAAG,WAAW,SAAS,MAAM,IAAI;gBAC1D;YACF;YAEA,IAAI,CAAC,kBAAkB,EAAE,EAAE;gBACzB,QAAQ,KAAK,CAAC;YAChB;YAEA,OAAO,IAAI,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;QAChC,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB;QAAqB;QAAiB;QAAuB;QAC7D;QAA6B;QAAY;QAAqB;QAC9D;QAAe;QAAqB;QAAyB;KAC9D;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;;sDAC3B,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAI1C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO1D,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;;8CACT,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAA+B;;;;;;;8CAGrD,8OAAC,gIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;oCACrC,uBACC,8OAAC;wCAAI,WAAU;kDACZ;;;;;;kDAKL,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAwB;;;;;;0DAEtC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,SAAQ;wDAAQ,WAAU;kEAAoC;;;;;;kEAGrE,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,aAAY;wDACZ,OAAO,SAAS,KAAK;wDACrB,UAAU;wDACV,QAAQ;;;;;;;;;;;;0DAIZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,SAAQ;wDAAc,WAAU;kEAAoC;;;;;;kEAG3E,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,aAAY;wDACZ,OAAO,SAAS,WAAW;wDAC3B,UAAU;wDACV,QAAQ;;;;;;;;;;;;0DAIZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,SAAQ;wDAAc,WAAU;kEAAoC;;;;;;kEAG3E,8OAAC;wDACC,IAAG;wDACH,MAAK;wDACL,MAAM;wDACN,WAAU;wDACV,aAAY;wDACZ,OAAO,SAAS,WAAW;wDAC3B,UAAU;;;;;;;;;;;;;;;;;;kDAMhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAwB;;;;;;0DAEtC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,SAAQ;gEAAa,WAAU;0EAAoC;;;;;;0EAG1E,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,UAAU;gEAC1B,UAAU;gEACV,QAAQ;;;;;;;;;;;;kEAIZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,SAAQ;gEAAW,WAAU;0EAAoC;;;;;;0EAGxE,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,QAAQ;gEACxB,UAAU;gEACV,QAAQ;;;;;;;;;;;;;;;;;;0DAKd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,SAAQ;wDAAS,WAAU;kEAAoC;;;;;;kEAGtE,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,MAAK;wDACL,aAAY;wDACZ,OAAO,SAAS,MAAM;wDACtB,UAAU;;;;;;;;;;;;;;;;;;kDAMhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAwB;;;;;;0DAEtC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAAoC;;;;;;kEAGrD,8OAAC;wDAAI,WAAU;kEACZ,gBAAgB,GAAG,CAAC,CAAC,yBACpB,8OAAC;gEAEC,MAAK;gEACL,SAAS,IAAM,qBAAqB;gEACpC,WAAW,CAAC,sDAAsD,EAChE,SAAS,SAAS,CAAC,QAAQ,CAAC,YACxB,8CACA,2DACJ;0EAED;+DATI;;;;;;;;;;;;;;;;0DAeb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,SAAQ;gEAAe,WAAU;0EAAoC;;;;;;0EAG5E,8OAAC;gEACC,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,YAAY;gEAC5B,UAAU;gEACV,WAAU;;kFAEV,8OAAC;wEAAO,OAAM;kFAAS;;;;;;kFACvB,8OAAC;wEAAO,OAAM;kFAAY;;;;;;kFAC1B,8OAAC;wEAAO,OAAM;kFAAS;;;;;;;;;;;;;;;;;;kEAI3B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,SAAQ;gEAAO,WAAU;0EAAoC;;;;;;0EAGpE,8OAAC;gEACC,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,IAAI;gEACpB,UAAU;gEACV,WAAU;;kFAEV,8OAAC;wEAAO,OAAM;kFAAU;;;;;;kFACxB,8OAAC;wEAAO,OAAM;kFAAW;;;;;;kFACzB,8OAAC;wEAAO,OAAM;kFAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAM/B,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,WAAU;wCAAS,MAAK;wCAAK,UAAU;kDAC1D,wBACC;;8DACE,8OAAC;oDAAI,WAAU;;;;;;gDAAuE;;yEAIxF;;8DACE,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWzD", "debugId": null}}]}