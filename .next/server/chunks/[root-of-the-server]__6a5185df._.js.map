{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/lib/supabase-server.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\nimport { Database } from './database.types'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Server-side Supabase client\nexport const createServerComponentClient = async () => {\n  const cookieStore = await cookies()\n  \n  return createServerClient<Database>(\n    supabaseUrl,\n    supabaseAnonKey,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n\n// Admin client for server-side operations\nexport const createAdminClient = () =>\n  createClient<Database>(\n    supabaseUrl,\n    process.env.SUPABASE_SERVICE_ROLE_KEY!,\n    {\n      auth: {\n        autoRefreshToken: false,\n        persistSession: false\n      }\n    }\n  )\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;;;;AAGA,MAAM;AACN,MAAM;AAGC,MAAM,8BAA8B;IACzC,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EACtB,aACA,iBACA;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ;AAGO,MAAM,oBAAoB,IAC/B,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EACT,aACA,QAAQ,GAAG,CAAC,yBAAyB,EACrC;QACE,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;QAClB;IACF", "debugId": null}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/app/api/votes/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createServerComponentClient } from '@/lib/supabase-server'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const supabase = await createServerComponentClient()\n    \n    // Get the current user\n    const { data: { user }, error: authError } = await supabase.auth.getUser()\n    \n    if (authError || !user) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const body = await request.json()\n    const { tripId, interests, budgetLevel, pace, additionalNotes } = body\n\n    // Validate required fields\n    if (!tripId) {\n      return NextResponse.json({ error: 'tripId is required' }, { status: 400 })\n    }\n\n    // Verify the user is invited to this trip or is the trip owner\n    const { data: trip, error: tripError } = await supabase\n      .from('trips')\n      .select('*')\n      .eq('id', tripId)\n      .single()\n\n    if (tripError || !trip) {\n      return NextResponse.json({ error: 'Trip not found' }, { status: 404 })\n    }\n\n    // Check if user is the trip owner or has an accepted invitation\n    const isOwner = trip.user_id === user.id\n    let hasInvitation = false\n\n    if (!isOwner) {\n      const { data: invitation } = await supabase\n        .from('trip_invitations')\n        .select('id')\n        .eq('trip_id', tripId)\n        .eq('email', user.email)\n        .eq('status', 'accepted')\n        .single()\n\n      hasInvitation = !!invitation\n    }\n\n    if (!isOwner && !hasInvitation) {\n      return NextResponse.json({ \n        error: 'You are not authorized to vote on this trip' \n      }, { status: 403 })\n    }\n\n    // Create or update the vote\n    const { data: vote, error: voteError } = await supabase\n      .from('trip_votes')\n      .upsert({\n        trip_id: tripId,\n        user_id: user.id,\n        interests: interests || [],\n        budget_level: budgetLevel,\n        pace: pace,\n        additional_notes: additionalNotes,\n      })\n      .select()\n      .single()\n\n    if (voteError) {\n      console.error('Error creating/updating vote:', voteError)\n      return NextResponse.json({ error: 'Failed to save vote' }, { status: 500 })\n    }\n\n    return NextResponse.json({ vote, message: 'Vote saved successfully' })\n\n  } catch (error) {\n    console.error('Error in POST /api/votes:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    const supabase = await createServerComponentClient()\n    \n    // Get the current user\n    const { data: { user }, error: authError } = await supabase.auth.getUser()\n    \n    if (authError || !user) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const { searchParams } = new URL(request.url)\n    const tripId = searchParams.get('tripId')\n\n    if (!tripId) {\n      return NextResponse.json({ error: 'tripId is required' }, { status: 400 })\n    }\n\n    // Verify the user can access this trip's votes\n    const { data: trip, error: tripError } = await supabase\n      .from('trips')\n      .select('*')\n      .eq('id', tripId)\n      .single()\n\n    if (tripError || !trip) {\n      return NextResponse.json({ error: 'Trip not found' }, { status: 404 })\n    }\n\n    // Check if user is the trip owner or has an accepted invitation\n    const isOwner = trip.user_id === user.id\n    let hasInvitation = false\n\n    if (!isOwner) {\n      const { data: invitation } = await supabase\n        .from('trip_invitations')\n        .select('id')\n        .eq('trip_id', tripId)\n        .eq('email', user.email)\n        .eq('status', 'accepted')\n        .single()\n\n      hasInvitation = !!invitation\n    }\n\n    if (!isOwner && !hasInvitation) {\n      return NextResponse.json({ \n        error: 'You are not authorized to view votes for this trip' \n      }, { status: 403 })\n    }\n\n    // Get all votes for the trip\n    const { data: votes, error } = await supabase\n      .from('trip_votes')\n      .select('*')\n      .eq('trip_id', tripId)\n      .order('voted_at', { ascending: false })\n\n    if (error) {\n      console.error('Error fetching votes:', error)\n      return NextResponse.json({ error: 'Failed to fetch votes' }, { status: 500 })\n    }\n\n    return NextResponse.json({ votes })\n  } catch (error) {\n    console.error('Error in GET /api/votes:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,8BAA2B,AAAD;QAEjD,uBAAuB;QACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG;QAElE,2BAA2B;QAC3B,IAAI,CAAC,QAAQ;YACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAqB,GAAG;gBAAE,QAAQ;YAAI;QAC1E;QAEA,+DAA+D;QAC/D,MAAM,EAAE,MAAM,IAAI,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAC5C,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAiB,GAAG;gBAAE,QAAQ;YAAI;QACtE;QAEA,gEAAgE;QAChE,MAAM,UAAU,KAAK,OAAO,KAAK,KAAK,EAAE;QACxC,IAAI,gBAAgB;QAEpB,IAAI,CAAC,SAAS;YACZ,MAAM,EAAE,MAAM,UAAU,EAAE,GAAG,MAAM,SAChC,IAAI,CAAC,oBACL,MAAM,CAAC,MACP,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,SAAS,KAAK,KAAK,EACtB,EAAE,CAAC,UAAU,YACb,MAAM;YAET,gBAAgB,CAAC,CAAC;QACpB;QAEA,IAAI,CAAC,WAAW,CAAC,eAAe;YAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,4BAA4B;QAC5B,MAAM,EAAE,MAAM,IAAI,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAC5C,IAAI,CAAC,cACL,MAAM,CAAC;YACN,SAAS;YACT,SAAS,KAAK,EAAE;YAChB,WAAW,aAAa,EAAE;YAC1B,cAAc;YACd,MAAM;YACN,kBAAkB;QACpB,GACC,MAAM,GACN,MAAM;QAET,IAAI,WAAW;YACb,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAsB,GAAG;gBAAE,QAAQ;YAAI;QAC3E;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;YAAM,SAAS;QAA0B;IAEtE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,8BAA2B,AAAD;QAEjD,uBAAuB;QACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC;QAEhC,IAAI,CAAC,QAAQ;YACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAqB,GAAG;gBAAE,QAAQ;YAAI;QAC1E;QAEA,+CAA+C;QAC/C,MAAM,EAAE,MAAM,IAAI,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAC5C,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAiB,GAAG;gBAAE,QAAQ;YAAI;QACtE;QAEA,gEAAgE;QAChE,MAAM,UAAU,KAAK,OAAO,KAAK,KAAK,EAAE;QACxC,IAAI,gBAAgB;QAEpB,IAAI,CAAC,SAAS;YACZ,MAAM,EAAE,MAAM,UAAU,EAAE,GAAG,MAAM,SAChC,IAAI,CAAC,oBACL,MAAM,CAAC,MACP,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,SAAS,KAAK,KAAK,EACtB,EAAE,CAAC,UAAU,YACb,MAAM;YAET,gBAAgB,CAAC,CAAC;QACpB;QAEA,IAAI,CAAC,WAAW,CAAC,eAAe;YAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,6BAA6B;QAC7B,MAAM,EAAE,MAAM,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,cACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,QACd,KAAK,CAAC,YAAY;YAAE,WAAW;QAAM;QAExC,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAwB,GAAG;gBAAE,QAAQ;YAAI;QAC7E;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAM;IACnC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}