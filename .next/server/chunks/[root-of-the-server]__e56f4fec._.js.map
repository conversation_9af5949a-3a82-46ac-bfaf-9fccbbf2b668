{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/lib/email.ts"], "sourcesContent": ["import { Resend } from 'resend'\n\nif (!process.env.RESEND_API_KEY) {\n  console.warn('RESEND_API_KEY is not set. Email functionality will be disabled.')\n}\n\nconst resend = new Resend(process.env.RESEND_API_KEY)\n\ninterface InvitationEmailData {\n  to: string\n  inviterName: string\n  inviterEmail: string\n  tripTitle: string\n  tripDescription?: string\n  destination: string\n  startDate: string\n  endDate: string\n  invitationToken: string\n  invitationUrl: string\n}\n\nexport const sendTripInvitation = async (data: InvitationEmailData) => {\n  if (!process.env.RESEND_API_KEY) {\n    console.log('Email would be sent to:', data.to)\n    console.log('Invitation URL:', data.invitationUrl)\n    return { success: false, error: 'Email service not configured' }\n  }\n\n  try {\n    const emailHtml = generateInvitationEmailHTML(data)\n    const emailText = generateInvitationEmailText(data)\n\n    console.log('Sending email to:', data.to)\n    console.log('Using Resend API key:', process.env.RESEND_API_KEY ? 'Set' : 'Not set')\n\n    const result = await resend.emails.send({\n      from: 'Travel Planner <<EMAIL>>', // Using Resend's default domain\n      to: [data.to],\n      subject: `🌍 You're invited to join \"${data.tripTitle}\"!`,\n      html: emailHtml,\n      text: emailText,\n    })\n\n    console.log('Email sent successfully:', result)\n\n    return { success: true, data: result }\n  } catch (error) {\n    console.error('Error sending invitation email:', error)\n    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }\n  }\n}\n\nconst generateInvitationEmailHTML = (data: InvitationEmailData) => {\n  return `\n<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Trip Invitation</title>\n    <style>\n        body {\n            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;\n            line-height: 1.6;\n            color: #333;\n            max-width: 600px;\n            margin: 0 auto;\n            padding: 20px;\n            background-color: #f8fafc;\n        }\n        .container {\n            background: white;\n            border-radius: 12px;\n            padding: 40px;\n            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n        }\n        .header {\n            text-align: center;\n            margin-bottom: 30px;\n        }\n        .logo {\n            font-size: 24px;\n            font-weight: bold;\n            color: #2563eb;\n            margin-bottom: 10px;\n        }\n        .trip-card {\n            background: #f1f5f9;\n            border-radius: 8px;\n            padding: 20px;\n            margin: 20px 0;\n        }\n        .trip-title {\n            font-size: 20px;\n            font-weight: bold;\n            color: #1e293b;\n            margin-bottom: 10px;\n        }\n        .trip-details {\n            color: #64748b;\n            margin-bottom: 8px;\n        }\n        .cta-button {\n            display: inline-block;\n            background: #2563eb;\n            color: white;\n            padding: 12px 24px;\n            text-decoration: none;\n            border-radius: 6px;\n            font-weight: 500;\n            margin: 20px 0;\n        }\n        .footer {\n            text-align: center;\n            margin-top: 30px;\n            padding-top: 20px;\n            border-top: 1px solid #e2e8f0;\n            color: #64748b;\n            font-size: 14px;\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <div class=\"header\">\n            <div class=\"logo\">🌍 Travel Planner</div>\n            <h1>You're Invited to Join a Trip!</h1>\n        </div>\n        \n        <p>Hi there!</p>\n        \n        <p><strong>${data.inviterName}</strong> (${data.inviterEmail}) has invited you to join their upcoming trip. They'd love to have you as part of the adventure!</p>\n        \n        <div class=\"trip-card\">\n            <div class=\"trip-title\">${data.tripTitle}</div>\n            ${data.tripDescription ? `<p>${data.tripDescription}</p>` : ''}\n            <div class=\"trip-details\">📍 <strong>Destination:</strong> ${data.destination}</div>\n            <div class=\"trip-details\">📅 <strong>Dates:</strong> ${data.startDate} - ${data.endDate}</div>\n        </div>\n        \n        <p>Before the itinerary is finalized, you'll have the chance to vote on your preferences for activities, budget level, and trip pace. Your input will help create the perfect trip for everyone!</p>\n        \n        <div style=\"text-align: center;\">\n            <a href=\"${data.invitationUrl}\" class=\"cta-button\">Accept Invitation & Vote</a>\n        </div>\n        \n        <p><strong>What happens next?</strong></p>\n        <ol>\n            <li>Click the button above to accept the invitation</li>\n            <li>Sign up or log in to your account</li>\n            <li>Vote on your trip preferences</li>\n            <li>Wait for the AI-generated itinerary based on everyone's votes!</li>\n        </ol>\n        \n        <p>If you don't want to join this trip, you can simply ignore this email.</p>\n        \n        <div class=\"footer\">\n            <p>This invitation will expire in 7 days.</p>\n            <p>Happy travels! 🧳</p>\n        </div>\n    </div>\n</body>\n</html>\n  `\n}\n\nconst generateInvitationEmailText = (data: InvitationEmailData) => {\n  return `\n🌍 Travel Planner - Trip Invitation\n\nHi there!\n\n${data.inviterName} (${data.inviterEmail}) has invited you to join their upcoming trip: \"${data.tripTitle}\"\n\nTrip Details:\n📍 Destination: ${data.destination}\n📅 Dates: ${data.startDate} - ${data.endDate}\n${data.tripDescription ? `\\nDescription: ${data.tripDescription}` : ''}\n\nBefore the itinerary is finalized, you'll have the chance to vote on your preferences for activities, budget level, and trip pace. Your input will help create the perfect trip for everyone!\n\nTo accept this invitation and vote on preferences, visit:\n${data.invitationUrl}\n\nWhat happens next?\n1. Click the link above to accept the invitation\n2. Sign up or log in to your account\n3. Vote on your trip preferences\n4. Wait for the AI-generated itinerary based on everyone's votes!\n\nIf you don't want to join this trip, you can simply ignore this email.\n\nThis invitation will expire in 7 days.\n\nHappy travels! 🧳\n\n---\nTravel Planner\n  `\n}\n\n// Helper function to format dates for email\nexport const formatDateForEmail = (dateString: string): string => {\n  const date = new Date(dateString)\n  return date.toLocaleDateString('en-US', {\n    weekday: 'long',\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  })\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,IAAI,CAAC,QAAQ,GAAG,CAAC,cAAc,EAAE;IAC/B,QAAQ,IAAI,CAAC;AACf;AAEA,MAAM,SAAS,IAAI,0IAAA,CAAA,SAAM,CAAC,QAAQ,GAAG,CAAC,cAAc;AAe7C,MAAM,qBAAqB,OAAO;IACvC,IAAI,CAAC,QAAQ,GAAG,CAAC,cAAc,EAAE;QAC/B,QAAQ,GAAG,CAAC,2BAA2B,KAAK,EAAE;QAC9C,QAAQ,GAAG,CAAC,mBAAmB,KAAK,aAAa;QACjD,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;IAEA,IAAI;QACF,MAAM,YAAY,4BAA4B;QAC9C,MAAM,YAAY,4BAA4B;QAE9C,QAAQ,GAAG,CAAC,qBAAqB,KAAK,EAAE;QACxC,QAAQ,GAAG,CAAC,yBAAyB,QAAQ,GAAG,CAAC,cAAc,GAAG,QAAQ;QAE1E,MAAM,SAAS,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC;YACtC,MAAM;YACN,IAAI;gBAAC,KAAK,EAAE;aAAC;YACb,SAAS,CAAC,2BAA2B,EAAE,KAAK,SAAS,CAAC,EAAE,CAAC;YACzD,MAAM;YACN,MAAM;QACR;QAEA,QAAQ,GAAG,CAAC,4BAA4B;QAExC,OAAO;YAAE,SAAS;YAAM,MAAM;QAAO;IACvC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;YAAE,SAAS;YAAO,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAAgB;IAC3F;AACF;AAEA,MAAM,8BAA8B,CAAC;IACnC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mBA8ES,EAAE,KAAK,WAAW,CAAC,WAAW,EAAE,KAAK,YAAY,CAAC;;;oCAGjC,EAAE,KAAK,SAAS,CAAC;YACzC,EAAE,KAAK,eAAe,GAAG,CAAC,GAAG,EAAE,KAAK,eAAe,CAAC,IAAI,CAAC,GAAG,GAAG;uEACJ,EAAE,KAAK,WAAW,CAAC;iEACzB,EAAE,KAAK,SAAS,CAAC,GAAG,EAAE,KAAK,OAAO,CAAC;;;;;;qBAM/E,EAAE,KAAK,aAAa,CAAC;;;;;;;;;;;;;;;;;;;;EAoBxC,CAAC;AACH;AAEA,MAAM,8BAA8B,CAAC;IACnC,OAAO,CAAC;;;;;AAKV,EAAE,KAAK,WAAW,CAAC,EAAE,EAAE,KAAK,YAAY,CAAC,gDAAgD,EAAE,KAAK,SAAS,CAAC;;;gBAG1F,EAAE,KAAK,WAAW,CAAC;UACzB,EAAE,KAAK,SAAS,CAAC,GAAG,EAAE,KAAK,OAAO,CAAC;AAC7C,EAAE,KAAK,eAAe,GAAG,CAAC,eAAe,EAAE,KAAK,eAAe,EAAE,GAAG,GAAG;;;;;AAKvE,EAAE,KAAK,aAAa,CAAC;;;;;;;;;;;;;;;;EAgBnB,CAAC;AACH;AAGO,MAAM,qBAAqB,CAAC;IACjC,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,SAAS;QACT,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF", "debugId": null}}, {"offset": {"line": 268, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/app/api/test-email/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { sendTripInvitation, formatDateForEmail } from '@/lib/email'\n\nexport async function POST(request: NextRequest) {\n  try {\n    console.log('Test email endpoint called')\n    \n    const { email } = await request.json()\n    \n    if (!email) {\n      return NextResponse.json({ error: 'Email is required' }, { status: 400 })\n    }\n\n    console.log('Sending test email to:', email)\n\n    // Send a test invitation email\n    const emailResult = await sendTripInvitation({\n      to: email,\n      inviterName: 'Test User',\n      inviterEmail: '<EMAIL>',\n      tripTitle: 'Test Trip to Paris',\n      tripDescription: 'A wonderful test trip to the City of Light',\n      destination: 'Paris, France',\n      startDate: formatDateForEmail('2025-07-01'),\n      endDate: formatDateForEmail('2025-07-07'),\n      invitationToken: 'test-token-123',\n      invitationUrl: `${process.env.NEXT_PUBLIC_APP_URL}/invite/test-token-123`,\n    })\n\n    console.log('Email result:', emailResult)\n\n    if (emailResult.success) {\n      return NextResponse.json({ \n        success: true, \n        message: 'Test email sent successfully!',\n        data: emailResult.data \n      })\n    } else {\n      return NextResponse.json({ \n        success: false, \n        error: emailResult.error \n      }, { status: 500 })\n    }\n\n  } catch (error) {\n    console.error('Error in test email endpoint:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEpC,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAoB,GAAG;gBAAE,QAAQ;YAAI;QACzE;QAEA,QAAQ,GAAG,CAAC,0BAA0B;QAEtC,+BAA+B;QAC/B,MAAM,cAAc,MAAM,CAAA,GAAA,qHAAA,CAAA,qBAAkB,AAAD,EAAE;YAC3C,IAAI;YACJ,aAAa;YACb,cAAc;YACd,WAAW;YACX,iBAAiB;YACjB,aAAa;YACb,WAAW,CAAA,GAAA,qHAAA,CAAA,qBAAkB,AAAD,EAAE;YAC9B,SAAS,CAAA,GAAA,qHAAA,CAAA,qBAAkB,AAAD,EAAE;YAC5B,iBAAiB;YACjB,eAAe,6DAAmC,sBAAsB,CAAC;QAC3E;QAEA,QAAQ,GAAG,CAAC,iBAAiB;QAE7B,IAAI,YAAY,OAAO,EAAE;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;gBACT,MAAM,YAAY,IAAI;YACxB;QACF,OAAO;YACL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO,YAAY,KAAK;YAC1B,GAAG;gBAAE,QAAQ;YAAI;QACnB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}