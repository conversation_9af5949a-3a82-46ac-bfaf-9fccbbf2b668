{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/lib/supabase-server.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\nimport { Database } from './database.types'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Server-side Supabase client\nexport const createServerComponentClient = async () => {\n  const cookieStore = await cookies()\n  \n  return createServerClient<Database>(\n    supabaseUrl,\n    supabaseAnonKey,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n\n// Admin client for server-side operations\nexport const createAdminClient = () =>\n  createClient<Database>(\n    supabaseUrl,\n    process.env.SUPABASE_SERVICE_ROLE_KEY!,\n    {\n      auth: {\n        autoRefreshToken: false,\n        persistSession: false\n      }\n    }\n  )\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;;;;AAGA,MAAM;AACN,MAAM;AAGC,MAAM,8BAA8B;IACzC,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EACtB,aACA,iBACA;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ;AAGO,MAAM,oBAAoB,IAC/B,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EACT,aACA,QAAQ,GAAG,CAAC,yBAAyB,EACrC;QACE,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;QAClB;IACF", "debugId": null}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/app/api/debug/trips/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createServerComponentClient } from '@/lib/supabase-server'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const supabase = await createServerComponentClient()\n    \n    // Get the current user\n    const { data: { user }, error: authError } = await supabase.auth.getUser()\n    \n    if (authError || !user) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    console.log('Debug: Fetching trips for user:', user.id)\n\n    // Get all trips for this user\n    const { data: trips, error } = await supabase\n      .from('trips')\n      .select('*')\n      .eq('user_id', user.id)\n      .order('created_at', { ascending: false })\n\n    if (error) {\n      console.error('Debug: Supabase error:', error)\n      return NextResponse.json({ error: 'Failed to fetch trips', details: error }, { status: 500 })\n    }\n\n    console.log('Debug: Found trips:', trips)\n\n    return NextResponse.json({ \n      user: { id: user.id, email: user.email },\n      trips: trips || [],\n      count: trips?.length || 0\n    })\n  } catch (error) {\n    console.error('Debug: Error:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,8BAA2B,AAAD;QAEjD,uBAAuB;QACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,QAAQ,GAAG,CAAC,mCAAmC,KAAK,EAAE;QAEtD,8BAA8B;QAC9B,MAAM,EAAE,MAAM,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;gBAAyB,SAAS;YAAM,GAAG;gBAAE,QAAQ;YAAI;QAC7F;QAEA,QAAQ,GAAG,CAAC,uBAAuB;QAEnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,MAAM;gBAAE,IAAI,KAAK,EAAE;gBAAE,OAAO,KAAK,KAAK;YAAC;YACvC,OAAO,SAAS,EAAE;YAClB,OAAO,OAAO,UAAU;QAC1B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}