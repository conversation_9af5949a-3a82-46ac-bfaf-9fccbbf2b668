module.exports = {

"[project]/node_modules/next/dist/compiled/react-dom/server.js [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/node_modules_next_dist_compiled_62374d6a._.js",
  "server/chunks/[root-of-the-server]__6052af13._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/next/dist/compiled/react-dom/server.js [app-route] (ecmascript)");
    });
});
}}),

};