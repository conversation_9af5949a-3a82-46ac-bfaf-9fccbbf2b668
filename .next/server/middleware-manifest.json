{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_632acfc5._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_7273925d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "HvhgbwCgZ5HolTkgBqTTK4bgm0GpoSkOLCwrPaqcPCs=", "__NEXT_PREVIEW_MODE_ID": "2a11bd8f3f99bfaec42b602aec46c0fa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "fd1adf634b59323af99e4b5fd956efe242be04d6aed07115b8a6e7726b2d4eab", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0a4e21ede80ea777beb7e026c8313f129bb19c0d072939be7b037b2bd6249786"}}}, "sortedMiddleware": ["/"], "functions": {}}