{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: string | Date): string {\n  return new Date(date).toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  })\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(amount)\n}\n\nexport function calculateTripDuration(startDate: string, endDate: string): number {\n  const start = new Date(startDate)\n  const end = new Date(endDate)\n  const diffTime = Math.abs(end.getTime() - start.getTime())\n  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))\n  return diffDays\n}\n\nexport function generateTripId(): string {\n  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w ]+/g, '')\n    .replace(/ +/g, '-')\n}\n\nexport function formatDateForEmail(dateString: string): string {\n  const date = new Date(dateString)\n  return date.toLocaleDateString('en-US', {\n    weekday: 'long',\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  })\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;QAChD,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,sBAAsB,SAAiB,EAAE,OAAe;IACtE,MAAM,QAAQ,IAAI,KAAK;IACvB,MAAM,MAAM,IAAI,KAAK;IACrB,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,OAAO,KAAK,MAAM,OAAO;IACvD,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IAC1D,OAAO;AACT;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;AAC/F;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,OAAO;AACpB;AAEO,SAAS,mBAAmB,UAAkB;IACnD,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,SAAS;QACT,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-white text-gray-900 shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-gray-600\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/app/email-preview/page.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'\n\n// This is a preview page to see how the email will look\nexport default function EmailPreviewPage() {\n  const sampleData = {\n    inviterName: '<PERSON>',\n    inviterEmail: '<EMAIL>',\n    tripTitle: 'Amazing Summer Adventure in Japan',\n    tripDescription: 'Join us for an incredible journey through Tokyo, Kyoto, and Osaka. We\\'ll explore ancient temples, modern cities, and delicious cuisine!',\n    destination: 'Tokyo, Japan',\n    startDate: 'Saturday, July 15, 2024',\n    endDate: 'Sunday, July 28, 2024',\n    invitationUrl: 'http://localhost:3000/invite/sample-token-123'\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-8\">\n      <div className=\"container mx-auto px-4 max-w-4xl\">\n        <Card className=\"mb-8\">\n          <CardHeader>\n            <CardTitle>Email Template Preview</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <p className=\"text-gray-600\">\n              This is how the invitation email will look when sent to invitees.\n            </p>\n          </CardContent>\n        </Card>\n\n        {/* Email Preview */}\n        <div className=\"bg-white rounded-lg shadow-lg overflow-hidden\">\n          <div style={{\n            fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Oxygen, Ubuntu, Cantarell, sans-serif',\n            lineHeight: '1.6',\n            color: '#333',\n            maxWidth: '600px',\n            margin: '0 auto',\n            padding: '20px',\n            backgroundColor: '#f8fafc'\n          }}>\n            <div style={{\n              background: 'white',\n              borderRadius: '12px',\n              padding: '40px',\n              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'\n            }}>\n              <div style={{ textAlign: 'center', marginBottom: '30px' }}>\n                <div style={{\n                  fontSize: '24px',\n                  fontWeight: 'bold',\n                  color: '#2563eb',\n                  marginBottom: '10px'\n                }}>\n                  🌍 Travel Planner\n                </div>\n                <h1>You're Invited to Join a Trip!</h1>\n              </div>\n              \n              <p>Hi there!</p>\n              \n              <p>\n                <strong>{sampleData.inviterName}</strong> ({sampleData.inviterEmail}) has invited you to join their upcoming trip. They'd love to have you as part of the adventure!\n              </p>\n              \n              <div style={{\n                background: '#f1f5f9',\n                borderRadius: '8px',\n                padding: '20px',\n                margin: '20px 0'\n              }}>\n                <div style={{\n                  fontSize: '20px',\n                  fontWeight: 'bold',\n                  color: '#1e293b',\n                  marginBottom: '10px'\n                }}>\n                  {sampleData.tripTitle}\n                </div>\n                <p>{sampleData.tripDescription}</p>\n                <div style={{ color: '#64748b', marginBottom: '8px' }}>\n                  📍 <strong>Destination:</strong> {sampleData.destination}\n                </div>\n                <div style={{ color: '#64748b', marginBottom: '8px' }}>\n                  📅 <strong>Dates:</strong> {sampleData.startDate} - {sampleData.endDate}\n                </div>\n              </div>\n              \n              <p>Before the itinerary is finalized, you'll have the chance to vote on your preferences for activities, budget level, and trip pace. Your input will help create the perfect trip for everyone!</p>\n              \n              <div style={{ textAlign: 'center' }}>\n                <a \n                  href={sampleData.invitationUrl}\n                  style={{\n                    display: 'inline-block',\n                    background: '#2563eb',\n                    color: 'white',\n                    padding: '12px 24px',\n                    textDecoration: 'none',\n                    borderRadius: '6px',\n                    fontWeight: '500',\n                    margin: '20px 0'\n                  }}\n                >\n                  Accept Invitation & Vote\n                </a>\n              </div>\n              \n              <p><strong>What happens next?</strong></p>\n              <ol>\n                <li>Click the button above to accept the invitation</li>\n                <li>Sign up or log in to your account</li>\n                <li>Vote on your trip preferences</li>\n                <li>Wait for the AI-generated itinerary based on everyone's votes!</li>\n              </ol>\n              \n              <p>If you don't want to join this trip, you can simply ignore this email.</p>\n              \n              <div style={{\n                textAlign: 'center',\n                marginTop: '30px',\n                paddingTop: '20px',\n                borderTop: '1px solid #e2e8f0',\n                color: '#64748b',\n                fontSize: '14px'\n              }}>\n                <p>This invitation will expire in 7 days.</p>\n                <p>Happy travels! 🧳</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAKe,SAAS;IACtB,MAAM,aAAa;QACjB,aAAa;QACb,cAAc;QACd,WAAW;QACX,iBAAiB;QACjB,aAAa;QACb,WAAW;QACX,SAAS;QACT,eAAe;IACjB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;8BAOjC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,OAAO;4BACV,YAAY;4BACZ,YAAY;4BACZ,OAAO;4BACP,UAAU;4BACV,QAAQ;4BACR,SAAS;4BACT,iBAAiB;wBACnB;kCACE,cAAA,6LAAC;4BAAI,OAAO;gCACV,YAAY;gCACZ,cAAc;gCACd,SAAS;gCACT,WAAW;4BACb;;8CACE,6LAAC;oCAAI,OAAO;wCAAE,WAAW;wCAAU,cAAc;oCAAO;;sDACtD,6LAAC;4CAAI,OAAO;gDACV,UAAU;gDACV,YAAY;gDACZ,OAAO;gDACP,cAAc;4CAChB;sDAAG;;;;;;sDAGH,6LAAC;sDAAG;;;;;;;;;;;;8CAGN,6LAAC;8CAAE;;;;;;8CAEH,6LAAC;;sDACC,6LAAC;sDAAQ,WAAW,WAAW;;;;;;wCAAU;wCAAG,WAAW,YAAY;wCAAC;;;;;;;8CAGtE,6LAAC;oCAAI,OAAO;wCACV,YAAY;wCACZ,cAAc;wCACd,SAAS;wCACT,QAAQ;oCACV;;sDACE,6LAAC;4CAAI,OAAO;gDACV,UAAU;gDACV,YAAY;gDACZ,OAAO;gDACP,cAAc;4CAChB;sDACG,WAAW,SAAS;;;;;;sDAEvB,6LAAC;sDAAG,WAAW,eAAe;;;;;;sDAC9B,6LAAC;4CAAI,OAAO;gDAAE,OAAO;gDAAW,cAAc;4CAAM;;gDAAG;8DAClD,6LAAC;8DAAO;;;;;;gDAAqB;gDAAE,WAAW,WAAW;;;;;;;sDAE1D,6LAAC;4CAAI,OAAO;gDAAE,OAAO;gDAAW,cAAc;4CAAM;;gDAAG;8DAClD,6LAAC;8DAAO;;;;;;gDAAe;gDAAE,WAAW,SAAS;gDAAC;gDAAI,WAAW,OAAO;;;;;;;;;;;;;8CAI3E,6LAAC;8CAAE;;;;;;8CAEH,6LAAC;oCAAI,OAAO;wCAAE,WAAW;oCAAS;8CAChC,cAAA,6LAAC;wCACC,MAAM,WAAW,aAAa;wCAC9B,OAAO;4CACL,SAAS;4CACT,YAAY;4CACZ,OAAO;4CACP,SAAS;4CACT,gBAAgB;4CAChB,cAAc;4CACd,YAAY;4CACZ,QAAQ;wCACV;kDACD;;;;;;;;;;;8CAKH,6LAAC;8CAAE,cAAA,6LAAC;kDAAO;;;;;;;;;;;8CACX,6LAAC;;sDACC,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;8CAGN,6LAAC;8CAAE;;;;;;8CAEH,6LAAC;oCAAI,OAAO;wCACV,WAAW;wCACX,WAAW;wCACX,YAAY;wCACZ,WAAW;wCACX,OAAO;wCACP,UAAU;oCACZ;;sDACE,6LAAC;sDAAE;;;;;;sDACH,6LAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB;KAnIwB", "debugId": null}}]}