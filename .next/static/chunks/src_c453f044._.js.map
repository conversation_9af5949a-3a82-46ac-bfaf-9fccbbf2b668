{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient } from '@supabase/ssr'\nimport { Database } from './database.types'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Client-side Supabase client\nexport const createClientComponentClient = () =>\n  createBrowserClient<Database>(supabaseUrl, supabaseAnonKey)\n\n// Simple client for basic operations\nexport const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey)\n"], "names": [], "mappings": ";;;;AAIoB;AAJpB;AACA;AAAA;;;AAGA,MAAM;AACN,MAAM;AAGC,MAAM,8BAA8B,IACzC,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAY,aAAa;AAGtC,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAY,aAAa", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/hooks/useAuth.ts"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { User } from '@supabase/supabase-js'\nimport { createClientComponentClient } from '@/lib/supabase'\nimport { useRouter } from 'next/navigation'\n\nexport function useAuth() {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n  const supabase = createClientComponentClient()\n  const router = useRouter()\n\n  useEffect(() => {\n    const getUser = async () => {\n      const { data: { user } } = await supabase.auth.getUser()\n      setUser(user)\n      setLoading(false)\n    }\n\n    getUser()\n\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        setUser(session?.user ?? null)\n        setLoading(false)\n        \n        if (event === 'SIGNED_IN') {\n          router.push('/dashboard')\n        } else if (event === 'SIGNED_OUT') {\n          router.push('/')\n        }\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [supabase, router])\n\n  const signUp = async (email: string, password: string, fullName: string) => {\n    const { data, error } = await supabase.auth.signUp({\n      email,\n      password,\n      options: {\n        data: {\n          full_name: fullName,\n        },\n      },\n    })\n\n    if (error) throw error\n    return data\n  }\n\n  const signIn = async (email: string, password: string) => {\n    const { data, error } = await supabase.auth.signInWithPassword({\n      email,\n      password,\n    })\n\n    if (error) throw error\n    return data\n  }\n\n  const signOut = async () => {\n    const { error } = await supabase.auth.signOut()\n    if (error) throw error\n  }\n\n  const resetPassword = async (email: string) => {\n    const { error } = await supabase.auth.resetPasswordForEmail(email, {\n      redirectTo: `${window.location.origin}/auth/reset-password`,\n    })\n\n    if (error) throw error\n  }\n\n  return {\n    user,\n    loading,\n    signUp,\n    signIn,\n    signOut,\n    resetPassword,\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AAEA;AACA;;AALA;;;;AAOO,SAAS;;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,8BAA2B,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM;6CAAU;oBACd,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;oBACtD,QAAQ;oBACR,WAAW;gBACb;;YAEA;YAEA,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB;qCAChE,OAAO,OAAO;oBACZ,QAAQ,SAAS,QAAQ;oBACzB,WAAW;oBAEX,IAAI,UAAU,aAAa;wBACzB,OAAO,IAAI,CAAC;oBACd,OAAO,IAAI,UAAU,cAAc;wBACjC,OAAO,IAAI,CAAC;oBACd;gBACF;;YAGF;qCAAO,IAAM,aAAa,WAAW;;QACvC;4BAAG;QAAC;QAAU;KAAO;IAErB,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;YACjD;YACA;YACA,SAAS;gBACP,MAAM;oBACJ,WAAW;gBACb;YACF;QACF;QAEA,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;YAC7D;YACA;QACF;QAEA,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,UAAU;QACd,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAC7C,IAAI,OAAO,MAAM;IACnB;IAEA,MAAM,gBAAgB,OAAO;QAC3B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,qBAAqB,CAAC,OAAO;YACjE,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,oBAAoB,CAAC;QAC7D;QAEA,IAAI,OAAO,MAAM;IACnB;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GA7EgB;;QAIC,qIAAA,CAAA,YAAS", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: string | Date): string {\n  return new Date(date).toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  })\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(amount)\n}\n\nexport function calculateTripDuration(startDate: string, endDate: string): number {\n  const start = new Date(startDate)\n  const end = new Date(endDate)\n  const diffTime = Math.abs(end.getTime() - start.getTime())\n  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))\n  return diffDays\n}\n\nexport function generateTripId(): string {\n  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w ]+/g, '')\n    .replace(/ +/g, '-')\n}\n\nexport function formatDateForEmail(dateString: string): string {\n  const date = new Date(dateString)\n  return date.toLocaleDateString('en-US', {\n    weekday: 'long',\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  })\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;QAChD,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,sBAAsB,SAAiB,EAAE,OAAe;IACtE,MAAM,QAAQ,IAAI,KAAK;IACvB,MAAM,MAAM,IAAI,KAAK;IACrB,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,OAAO,KAAK,MAAM,OAAO;IACvD,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IAC1D,OAAO;AACT;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;AAC/F;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,OAAO;AACpB;AAEO,SAAS,mBAAmB,UAAkB;IACnD,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,SAAS;QACT,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-white transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-blue-600 text-white hover:bg-blue-700 shadow\",\n        destructive:\n          \"bg-red-600 text-white hover:bg-red-700 shadow\",\n        outline:\n          \"border border-gray-300 bg-white text-gray-900 hover:bg-gray-50 shadow-sm\",\n        secondary:\n          \"bg-gray-100 text-gray-900 hover:bg-gray-200 shadow-sm\",\n        ghost: \"hover:bg-gray-100 hover:text-gray-900\",\n        link: \"text-blue-600 underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,yRACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-white text-gray-900 shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-gray-600\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 365, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/app/trip/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport Link from 'next/link'\nimport { useAuth } from '@/hooks/useAuth'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { MapPin, ArrowLeft, Calendar, DollarSign, Users, Mail, CheckCircle, Clock, X, Vote } from 'lucide-react'\nimport { formatDate, formatCurrency } from '@/lib/utils'\n\ninterface Trip {\n  id: string\n  user_id: string\n  title: string\n  description: string\n  destination: string\n  start_date: string\n  end_date: string\n  budget: number\n  preferences: any\n  itinerary: any\n  created_at: string\n}\n\ninterface Invitation {\n  id: string\n  email: string\n  status: 'pending' | 'accepted' | 'declined'\n  created_at: string\n  invited_user?: {\n    full_name: string\n  }\n}\n\nexport default function TripPage({ params }: { params: Promise<{ id: string }> }) {\n  const { user, loading: authLoading } = useAuth()\n  const [trip, setTrip] = useState<Trip | null>(null)\n  const [invitations, setInvitations] = useState<Invitation[]>([])\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState('')\n  const [tripId, setTripId] = useState<string>('')\n  const [hasVoted, setHasVoted] = useState(false)\n  const [checkingVote, setCheckingVote] = useState(false)\n\n  useEffect(() => {\n    const getTripId = async () => {\n      const resolvedParams = await params\n      setTripId(resolvedParams.id)\n    }\n    getTripId()\n  }, [params])\n\n  useEffect(() => {\n    if (user && tripId) {\n      fetchTripData()\n      checkUserVoteStatus()\n    }\n  }, [user, tripId])\n\n  const fetchTripData = async () => {\n    try {\n      // Fetch trip details\n      const tripResponse = await fetch(`/api/trips/${tripId}`)\n      const tripData = await tripResponse.json()\n\n      if (!tripResponse.ok) {\n        setError(tripData.error || 'Failed to load trip')\n        return\n      }\n\n      setTrip(tripData.trip)\n\n      // Fetch invitations\n      const invitationsResponse = await fetch(`/api/invitations?tripId=${tripId}`)\n      const invitationsData = await invitationsResponse.json()\n\n      if (invitationsResponse.ok) {\n        setInvitations(invitationsData.invitations || [])\n      }\n\n    } catch (err) {\n      setError('Failed to load trip')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const checkUserVoteStatus = async () => {\n    if (!user || !tripId) return\n\n    setCheckingVote(true)\n    try {\n      // Check if user has voted by looking for their vote\n      const response = await fetch(`/api/votes?tripId=${tripId}`)\n      const data = await response.json()\n\n      if (response.ok && data.votes) {\n        // Check if current user has a vote\n        const userVote = data.votes.find((vote: any) => vote.user_id === user.id)\n        setHasVoted(!!userVote)\n      }\n    } catch (err) {\n      console.error('Error checking vote status:', err)\n    } finally {\n      setCheckingVote(false)\n    }\n  }\n\n  const canUserVote = () => {\n    if (!trip || !user) return false\n\n    // User can vote if they are the trip creator\n    if (trip.user_id === user.id) return true\n\n    // Or if they have an accepted invitation\n    const userInvitation = invitations.find(inv => inv.email === user.email)\n    return userInvitation && userInvitation.status === 'accepted'\n  }\n\n  if (authLoading || loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading trip...</p>\n        </div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <Card>\n          <CardHeader>\n            <CardTitle>Authentication Required</CardTitle>\n            <CardDescription>Please sign in to view this trip.</CardDescription>\n          </CardHeader>\n          <CardContent>\n            <Link href=\"/auth/login\">\n              <Button>Sign In</Button>\n            </Link>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center p-4\">\n        <Card className=\"w-full max-w-md\">\n          <CardHeader className=\"text-center\">\n            <CardTitle>Error</CardTitle>\n            <CardDescription>{error}</CardDescription>\n          </CardHeader>\n          <CardContent className=\"text-center\">\n            <Link href=\"/dashboard\">\n              <Button>Go to Dashboard</Button>\n            </Link>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  if (!trip) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <p className=\"text-gray-600\">Trip not found</p>\n        </div>\n      </div>\n    )\n  }\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'accepted':\n        return <CheckCircle className=\"h-4 w-4 text-green-500\" />\n      case 'declined':\n        return <X className=\"h-4 w-4 text-red-500\" />\n      default:\n        return <Clock className=\"h-4 w-4 text-yellow-500\" />\n    }\n  }\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'accepted':\n        return 'Accepted'\n      case 'declined':\n        return 'Declined'\n      default:\n        return 'Pending'\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex items-center space-x-4\">\n            <Link href=\"/dashboard\">\n              <Button variant=\"ghost\" size=\"sm\">\n                <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                Back to Dashboard\n              </Button>\n            </Link>\n            <div className=\"flex items-center space-x-2\">\n              <MapPin className=\"h-6 w-6 text-blue-600\" />\n              <span className=\"text-xl font-bold text-gray-900\">Trip Details</span>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"container mx-auto px-4 py-8 max-w-4xl\">\n        {/* Trip Overview */}\n        <Card className=\"mb-8\">\n          <CardHeader>\n            <div className=\"flex justify-between items-start\">\n              <div>\n                <CardTitle className=\"text-2xl\">{trip.title}</CardTitle>\n                <CardDescription>\n                  {trip.description}\n                </CardDescription>\n              </div>\n              {canUserVote() && (\n                <div className=\"flex-shrink-0\">\n                  <Link href={`/trip/${tripId}/vote`}>\n                    <Button\n                      variant={hasVoted ? \"outline\" : \"default\"}\n                      disabled={checkingVote}\n                    >\n                      <Vote className=\"h-4 w-4 mr-2\" />\n                      {checkingVote ? 'Checking...' : hasVoted ? 'Edit Vote' : 'Vote'}\n                    </Button>\n                  </Link>\n                </div>\n              )}\n            </div>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid md:grid-cols-3 gap-4\">\n              <div className=\"flex items-center\">\n                <MapPin className=\"h-5 w-5 mr-2 text-gray-500\" />\n                <span>{trip.destination}</span>\n              </div>\n              <div className=\"flex items-center\">\n                <Calendar className=\"h-5 w-5 mr-2 text-gray-500\" />\n                <span>{formatDate(trip.start_date)} - {formatDate(trip.end_date)}</span>\n              </div>\n              {trip.budget && (\n                <div className=\"flex items-center\">\n                  <DollarSign className=\"h-5 w-5 mr-2 text-gray-500\" />\n                  <span>{formatCurrency(trip.budget)}</span>\n                </div>\n              )}\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Invitations */}\n        {invitations.length > 0 && (\n          <Card className=\"mb-8\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <Users className=\"h-5 w-5 mr-2\" />\n                Travel Companions ({invitations.length})\n              </CardTitle>\n              <CardDescription>\n                People you've invited to join this trip\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3\">\n                {invitations.map((invitation) => (\n                  <div key={invitation.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                    <div className=\"flex items-center space-x-3\">\n                      <Mail className=\"h-4 w-4 text-gray-500\" />\n                      <div>\n                        <p className=\"font-medium\">\n                          {invitation.invited_user?.full_name || invitation.email}\n                        </p>\n                        {invitation.invited_user?.full_name && (\n                          <p className=\"text-sm text-gray-500\">{invitation.email}</p>\n                        )}\n                      </div>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      {getStatusIcon(invitation.status)}\n                      <span className=\"text-sm font-medium\">\n                        {getStatusText(invitation.status)}\n                      </span>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        )}\n\n        {/* Trip Preferences */}\n        {trip.preferences && (\n          <Card className=\"mb-8\">\n            <CardHeader>\n              <CardTitle>Trip Preferences</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid md:grid-cols-2 gap-6\">\n                {trip.preferences.interests && trip.preferences.interests.length > 0 && (\n                  <div>\n                    <h4 className=\"font-semibold mb-2\">Interests</h4>\n                    <div className=\"flex flex-wrap gap-2\">\n                      {trip.preferences.interests.map((interest: string) => (\n                        <span\n                          key={interest}\n                          className=\"px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded-md\"\n                        >\n                          {interest}\n                        </span>\n                      ))}\n                    </div>\n                  </div>\n                )}\n                <div>\n                  <h4 className=\"font-semibold mb-2\">Trip Style</h4>\n                  <div className=\"space-y-1 text-sm\">\n                    <p><span className=\"font-medium\">Budget Level:</span> {trip.preferences.budget_level}</p>\n                    <p><span className=\"font-medium\">Pace:</span> {trip.preferences.pace}</p>\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        )}\n\n        {/* Itinerary */}\n        {trip.itinerary ? (\n          <Card>\n            <CardHeader>\n              <CardTitle>Itinerary</CardTitle>\n              <CardDescription>\n                AI-generated itinerary based on your preferences\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-gray-600\">Itinerary details will be displayed here...</p>\n              {/* TODO: Add itinerary display component */}\n            </CardContent>\n          </Card>\n        ) : (\n          <Card>\n            <CardHeader>\n              <CardTitle>Itinerary</CardTitle>\n              <CardDescription>\n                {invitations.length > 0 \n                  ? \"Waiting for all invitees to vote on preferences before generating itinerary\"\n                  : \"Ready to generate your personalized itinerary\"\n                }\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              {invitations.length === 0 && (\n                <Button>\n                  Generate Itinerary\n                </Button>\n              )}\n              {invitations.length > 0 && (\n                <div className=\"text-center py-8\">\n                  <Clock className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                  <p className=\"text-gray-600\">\n                    Once all invited travelers have voted on their preferences, \n                    the AI will generate a personalized itinerary for your group.\n                  </p>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        )}\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AARA;;;;;;;;AAkCe,SAAS,SAAS,EAAE,MAAM,EAAuC;;IAC9E,MAAM,EAAE,IAAI,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM;gDAAY;oBAChB,MAAM,iBAAiB,MAAM;oBAC7B,UAAU,eAAe,EAAE;gBAC7B;;YACA;QACF;6BAAG;QAAC;KAAO;IAEX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,QAAQ,QAAQ;gBAClB;gBACA;YACF;QACF;6BAAG;QAAC;QAAM;KAAO;IAEjB,MAAM,gBAAgB;QACpB,IAAI;YACF,qBAAqB;YACrB,MAAM,eAAe,MAAM,MAAM,CAAC,WAAW,EAAE,QAAQ;YACvD,MAAM,WAAW,MAAM,aAAa,IAAI;YAExC,IAAI,CAAC,aAAa,EAAE,EAAE;gBACpB,SAAS,SAAS,KAAK,IAAI;gBAC3B;YACF;YAEA,QAAQ,SAAS,IAAI;YAErB,oBAAoB;YACpB,MAAM,sBAAsB,MAAM,MAAM,CAAC,wBAAwB,EAAE,QAAQ;YAC3E,MAAM,kBAAkB,MAAM,oBAAoB,IAAI;YAEtD,IAAI,oBAAoB,EAAE,EAAE;gBAC1B,eAAe,gBAAgB,WAAW,IAAI,EAAE;YAClD;QAEF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,QAAQ,CAAC,QAAQ;QAEtB,gBAAgB;QAChB,IAAI;YACF,oDAAoD;YACpD,MAAM,WAAW,MAAM,MAAM,CAAC,kBAAkB,EAAE,QAAQ;YAC1D,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,IAAI,KAAK,KAAK,EAAE;gBAC7B,mCAAmC;gBACnC,MAAM,WAAW,KAAK,KAAK,CAAC,IAAI,CAAC,CAAC,OAAc,KAAK,OAAO,KAAK,KAAK,EAAE;gBACxE,YAAY,CAAC,CAAC;YAChB;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,+BAA+B;QAC/C,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,QAAQ,CAAC,MAAM,OAAO;QAE3B,6CAA6C;QAC7C,IAAI,KAAK,OAAO,KAAK,KAAK,EAAE,EAAE,OAAO;QAErC,yCAAyC;QACzC,MAAM,iBAAiB,YAAY,IAAI,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK,KAAK,KAAK;QACvE,OAAO,kBAAkB,eAAe,MAAM,KAAK;IACrD;IAEA,IAAI,eAAe,SAAS;QAC1B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAEnB,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;0CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMpB;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;0CAAE;;;;;;;;;;;;kCAEpB,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;0CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMpB;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;IAIrC;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,+LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;;YACtB;gBACE,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;;sDAC3B,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAI1C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO1D,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAY,KAAK,KAAK;;;;;;8DAC3C,6LAAC,mIAAA,CAAA,kBAAe;8DACb,KAAK,WAAW;;;;;;;;;;;;wCAGpB,+BACC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAM,CAAC,MAAM,EAAE,OAAO,KAAK,CAAC;0DAChC,cAAA,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAS,WAAW,YAAY;oDAChC,UAAU;;sEAEV,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDACf,eAAe,gBAAgB,WAAW,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOrE,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;8DAAM,KAAK,WAAW;;;;;;;;;;;;sDAEzB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;;wDAAM,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,UAAU;wDAAE;wDAAI,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,QAAQ;;;;;;;;;;;;;wCAEhE,KAAK,MAAM,kBACV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,6LAAC;8DAAM,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQ1C,YAAY,MAAM,GAAG,mBACpB,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;4CACd,YAAY,MAAM;4CAAC;;;;;;;kDAEzC,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,2BAChB,6LAAC;4CAAwB,WAAU;;8DACjC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EACV,WAAW,YAAY,EAAE,aAAa,WAAW,KAAK;;;;;;gEAExD,WAAW,YAAY,EAAE,2BACxB,6LAAC;oEAAE,WAAU;8EAAyB,WAAW,KAAK;;;;;;;;;;;;;;;;;;8DAI5D,6LAAC;oDAAI,WAAU;;wDACZ,cAAc,WAAW,MAAM;sEAChC,6LAAC;4DAAK,WAAU;sEACb,cAAc,WAAW,MAAM;;;;;;;;;;;;;2CAf5B,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;oBA0BhC,KAAK,WAAW,kBACf,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;8CAAC;;;;;;;;;;;0CAEb,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;;wCACZ,KAAK,WAAW,CAAC,SAAS,IAAI,KAAK,WAAW,CAAC,SAAS,CAAC,MAAM,GAAG,mBACjE,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,6LAAC;oDAAI,WAAU;8DACZ,KAAK,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,yBAC/B,6LAAC;4DAEC,WAAU;sEAET;2DAHI;;;;;;;;;;;;;;;;sDASf,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EAAE,6LAAC;oEAAK,WAAU;8EAAc;;;;;;gEAAoB;gEAAE,KAAK,WAAW,CAAC,YAAY;;;;;;;sEACpF,6LAAC;;8EAAE,6LAAC;oEAAK,WAAU;8EAAc;;;;;;gEAAY;gEAAE,KAAK,WAAW,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAS/E,KAAK,SAAS,iBACb,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;6CAKjC,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,mIAAA,CAAA,kBAAe;kDACb,YAAY,MAAM,GAAG,IAClB,gFACA;;;;;;;;;;;;0CAIR,6LAAC,mIAAA,CAAA,cAAW;;oCACT,YAAY,MAAM,KAAK,mBACtB,6LAAC,qIAAA,CAAA,SAAM;kDAAC;;;;;;oCAIT,YAAY,MAAM,GAAG,mBACpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY/C;GAhWwB;;QACiB,0HAAA,CAAA,UAAO;;;KADxB", "debugId": null}}]}