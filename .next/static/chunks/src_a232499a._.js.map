{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient } from '@supabase/ssr'\nimport { Database } from './database.types'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Client-side Supabase client\nexport const createClientComponentClient = () =>\n  createBrowserClient<Database>(supabaseUrl, supabaseAnonKey)\n\n// Simple client for basic operations\nexport const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey)\n"], "names": [], "mappings": ";;;;AAIoB;AAJpB;AACA;AAAA;;;AAGA,MAAM;AACN,MAAM;AAGC,MAAM,8BAA8B,IACzC,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAY,aAAa;AAGtC,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAY,aAAa", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/hooks/useAuth.ts"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { User } from '@supabase/supabase-js'\nimport { createClientComponentClient } from '@/lib/supabase'\nimport { useRouter } from 'next/navigation'\n\nexport function useAuth() {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n  const supabase = createClientComponentClient()\n  const router = useRouter()\n\n  useEffect(() => {\n    const getUser = async () => {\n      const { data: { user } } = await supabase.auth.getUser()\n      setUser(user)\n      setLoading(false)\n    }\n\n    getUser()\n\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        setUser(session?.user ?? null)\n        setLoading(false)\n        \n        if (event === 'SIGNED_IN') {\n          router.push('/dashboard')\n        } else if (event === 'SIGNED_OUT') {\n          router.push('/')\n        }\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [supabase, router])\n\n  const signUp = async (email: string, password: string, fullName: string) => {\n    const { data, error } = await supabase.auth.signUp({\n      email,\n      password,\n      options: {\n        data: {\n          full_name: fullName,\n        },\n      },\n    })\n\n    if (error) throw error\n    return data\n  }\n\n  const signIn = async (email: string, password: string) => {\n    const { data, error } = await supabase.auth.signInWithPassword({\n      email,\n      password,\n    })\n\n    if (error) throw error\n    return data\n  }\n\n  const signOut = async () => {\n    const { error } = await supabase.auth.signOut()\n    if (error) throw error\n  }\n\n  const resetPassword = async (email: string) => {\n    const { error } = await supabase.auth.resetPasswordForEmail(email, {\n      redirectTo: `${window.location.origin}/auth/reset-password`,\n    })\n\n    if (error) throw error\n  }\n\n  return {\n    user,\n    loading,\n    signUp,\n    signIn,\n    signOut,\n    resetPassword,\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AAEA;AACA;;AALA;;;;AAOO,SAAS;;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,8BAA2B,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM;6CAAU;oBACd,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;oBACtD,QAAQ;oBACR,WAAW;gBACb;;YAEA;YAEA,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB;qCAChE,OAAO,OAAO;oBACZ,QAAQ,SAAS,QAAQ;oBACzB,WAAW;oBAEX,IAAI,UAAU,aAAa;wBACzB,OAAO,IAAI,CAAC;oBACd,OAAO,IAAI,UAAU,cAAc;wBACjC,OAAO,IAAI,CAAC;oBACd;gBACF;;YAGF;qCAAO,IAAM,aAAa,WAAW;;QACvC;4BAAG;QAAC;QAAU;KAAO;IAErB,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;YACjD;YACA;YACA,SAAS;gBACP,MAAM;oBACJ,WAAW;gBACb;YACF;QACF;QAEA,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;YAC7D;YACA;QACF;QAEA,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,UAAU;QACd,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAC7C,IAAI,OAAO,MAAM;IACnB;IAEA,MAAM,gBAAgB,OAAO;QAC3B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,qBAAqB,CAAC,OAAO;YACjE,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,oBAAoB,CAAC;QAC7D;QAEA,IAAI,OAAO,MAAM;IACnB;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GA7EgB;;QAIC,qIAAA,CAAA,YAAS", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: string | Date): string {\n  return new Date(date).toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  })\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(amount)\n}\n\nexport function calculateTripDuration(startDate: string, endDate: string): number {\n  const start = new Date(startDate)\n  const end = new Date(endDate)\n  const diffTime = Math.abs(end.getTime() - start.getTime())\n  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))\n  return diffDays\n}\n\nexport function generateTripId(): string {\n  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w ]+/g, '')\n    .replace(/ +/g, '-')\n}\n\nexport function formatDateForEmail(dateString: string): string {\n  const date = new Date(dateString)\n  return date.toLocaleDateString('en-US', {\n    weekday: 'long',\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  })\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;QAChD,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,sBAAsB,SAAiB,EAAE,OAAe;IACtE,MAAM,QAAQ,IAAI,KAAK;IACvB,MAAM,MAAM,IAAI,KAAK;IACrB,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,OAAO,KAAK,MAAM,OAAO;IACvD,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IAC1D,OAAO;AACT;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;AAC/F;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,OAAO;AACpB;AAEO,SAAS,mBAAmB,UAAkB;IACnD,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,SAAS;QACT,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-white transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-blue-600 text-white hover:bg-blue-700 shadow\",\n        destructive:\n          \"bg-red-600 text-white hover:bg-red-700 shadow\",\n        outline:\n          \"border border-gray-300 bg-white text-gray-900 hover:bg-gray-50 shadow-sm\",\n        secondary:\n          \"bg-gray-100 text-gray-900 hover:bg-gray-200 shadow-sm\",\n        ghost: \"hover:bg-gray-100 hover:text-gray-900\",\n        link: \"text-blue-600 underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,yRACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-white text-gray-900 shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-gray-600\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 365, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/p1/src/app/trip/%5Bid%5D/vote/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { useAuth } from '@/hooks/useAuth'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { MapPin, ArrowLeft, Vote, CheckCircle } from 'lucide-react'\nimport { formatDate } from '@/lib/utils'\n\ninterface Trip {\n  id: string\n  title: string\n  description: string\n  destination: string\n  start_date: string\n  end_date: string\n}\n\nexport default function VotePage({ params }: { params: Promise<{ id: string }> }) {\n  const { user, loading: authLoading } = useAuth()\n  const router = useRouter()\n\n  const [trip, setTrip] = useState<Trip | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [submitting, setSubmitting] = useState(false)\n  const [error, setError] = useState('')\n  const [success, setSuccess] = useState(false)\n  const [tripId, setTripId] = useState<string>('')\n  const [tripInterests, setTripInterests] = useState<string[]>([])\n\n  const [voteData, setVoteData] = useState({\n    interests: [] as string[],\n    budgetLevel: 'mid-range' as 'budget' | 'mid-range' | 'luxury',\n    pace: 'moderate' as 'relaxed' | 'moderate' | 'packed',\n    additionalNotes: '',\n  })\n\n  useEffect(() => {\n    const getTripId = async () => {\n      const resolvedParams = await params\n      setTripId(resolvedParams.id)\n    }\n    getTripId()\n  }, [params])\n\n  useEffect(() => {\n    if (user && tripId) {\n      fetchTrip()\n    }\n  }, [user, tripId])\n\n  const fetchTrip = async () => {\n    try {\n      const response = await fetch(`/api/trips/${tripId}`)\n      const data = await response.json()\n\n      if (!response.ok) {\n        setError(data.error || 'Failed to load trip')\n        return\n      }\n\n      setTrip(data.trip)\n\n      // Extract interests from trip preferences\n      if (data.trip.preferences && data.trip.preferences.interests) {\n        setTripInterests(data.trip.preferences.interests)\n      }\n    } catch (err) {\n      setError('Failed to load trip')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleInterestToggle = (interest: string) => {\n    setVoteData(prev => ({\n      ...prev,\n      interests: prev.interests.includes(interest)\n        ? prev.interests.filter(i => i !== interest)\n        : [...prev.interests, interest]\n    }))\n  }\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLSelectElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target\n    setVoteData(prev => ({ ...prev, [name]: value }))\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setSubmitting(true)\n    setError('')\n\n    try {\n      const response = await fetch('/api/votes', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          tripId: tripId,\n          interests: voteData.interests,\n          budgetLevel: voteData.budgetLevel,\n          pace: voteData.pace,\n          additionalNotes: voteData.additionalNotes,\n        }),\n      })\n\n      const data = await response.json()\n\n      if (!response.ok) {\n        setError(data.error || 'Failed to submit vote')\n        return\n      }\n\n      setSuccess(true)\n    } catch (err) {\n      setError('Failed to submit vote')\n    } finally {\n      setSubmitting(false)\n    }\n  }\n\n  if (authLoading || loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading trip...</p>\n        </div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    router.push('/auth/login')\n    return null\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center p-4\">\n        <Card className=\"w-full max-w-md\">\n          <CardHeader className=\"text-center\">\n            <CardTitle>Error</CardTitle>\n            <CardDescription>{error}</CardDescription>\n          </CardHeader>\n          <CardContent className=\"text-center\">\n            <Link href=\"/dashboard\">\n              <Button>Go to Dashboard</Button>\n            </Link>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  if (success) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center p-4\">\n        <Card className=\"w-full max-w-md\">\n          <CardHeader className=\"text-center\">\n            <CheckCircle className=\"h-16 w-16 text-green-500 mx-auto mb-4\" />\n            <CardTitle>Vote Submitted!</CardTitle>\n            <CardDescription>\n              Thank you for voting on the trip preferences. The trip organizer will use everyone's votes to create the perfect itinerary.\n            </CardDescription>\n          </CardHeader>\n          <CardContent className=\"text-center space-y-4\">\n            <Link href={`/trip/${tripId}`}>\n              <Button>View Trip</Button>\n            </Link>\n            <Link href=\"/dashboard\">\n              <Button variant=\"outline\" className=\"w-full\">\n                Go to Dashboard\n              </Button>\n            </Link>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  if (!trip) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <p className=\"text-gray-600\">Trip not found</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex items-center space-x-4\">\n            <Link href=\"/dashboard\">\n              <Button variant=\"ghost\" size=\"sm\">\n                <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                Back to Dashboard\n              </Button>\n            </Link>\n            <div className=\"flex items-center space-x-2\">\n              <Vote className=\"h-6 w-6 text-blue-600\" />\n              <span className=\"text-xl font-bold text-gray-900\">Vote on Trip Preferences</span>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"container mx-auto px-4 py-8 max-w-2xl\">\n        {/* Trip Info */}\n        <Card className=\"mb-8\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center\">\n              <MapPin className=\"h-6 w-6 mr-2 text-blue-600\" />\n              {trip.title}\n            </CardTitle>\n            <CardDescription>\n              {trip.destination} • {formatDate(trip.start_date)} - {formatDate(trip.end_date)}\n            </CardDescription>\n          </CardHeader>\n          {trip.description && (\n            <CardContent>\n              <p className=\"text-gray-600\">{trip.description}</p>\n            </CardContent>\n          )}\n        </Card>\n\n        {/* Voting Form */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Share Your Preferences</CardTitle>\n            <CardDescription>\n              Help create the perfect itinerary by voting on your preferences. Your input will be combined with other travelers' votes.\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              {/* Interests */}\n              <div className=\"space-y-4\">\n                <h3 className=\"text-lg font-semibold\">Interests</h3>\n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium text-gray-700\">\n                    What interests you most? (select all that apply)\n                  </label>\n                  <div className=\"grid grid-cols-2 md:grid-cols-3 gap-2\">\n                    {tripInterests.map((interest) => (\n                      <button\n                        key={interest}\n                        type=\"button\"\n                        onClick={() => handleInterestToggle(interest)}\n                        className={`text-sm px-3 py-2 rounded-md border transition-colors ${\n                          voteData.interests.includes(interest)\n                            ? 'bg-blue-600 border-blue-600 text-white shadow-md'\n                            : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400'\n                        }`}\n                      >\n                        {interest}\n                      </button>\n                    ))}\n                  </div>\n                </div>\n              </div>\n\n              {/* Budget Level */}\n              <div className=\"space-y-2\">\n                <label htmlFor=\"budgetLevel\" className=\"text-sm font-medium text-gray-700\">\n                  Preferred Budget Level\n                </label>\n                <select\n                  id=\"budgetLevel\"\n                  name=\"budgetLevel\"\n                  value={voteData.budgetLevel}\n                  onChange={handleInputChange}\n                  className=\"flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2\"\n                >\n                  <option value=\"budget\">Budget-friendly</option>\n                  <option value=\"mid-range\">Mid-range</option>\n                  <option value=\"luxury\">Luxury</option>\n                </select>\n              </div>\n\n              {/* Trip Pace */}\n              <div className=\"space-y-2\">\n                <label htmlFor=\"pace\" className=\"text-sm font-medium text-gray-700\">\n                  Preferred Trip Pace\n                </label>\n                <select\n                  id=\"pace\"\n                  name=\"pace\"\n                  value={voteData.pace}\n                  onChange={handleInputChange}\n                  className=\"flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2\"\n                >\n                  <option value=\"relaxed\">Relaxed - Plenty of downtime</option>\n                  <option value=\"moderate\">Moderate - Balanced schedule</option>\n                  <option value=\"packed\">Packed - See as much as possible</option>\n                </select>\n              </div>\n\n              {/* Additional Notes */}\n              <div className=\"space-y-2\">\n                <label htmlFor=\"additionalNotes\" className=\"text-sm font-medium text-gray-700\">\n                  Additional Notes (Optional)\n                </label>\n                <textarea\n                  id=\"additionalNotes\"\n                  name=\"additionalNotes\"\n                  rows={3}\n                  className=\"flex w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\"\n                  placeholder=\"Any specific requests, dietary restrictions, or other preferences...\"\n                  value={voteData.additionalNotes}\n                  onChange={handleInputChange}\n                />\n              </div>\n\n              {error && (\n                <div className=\"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm\">\n                  {error}\n                </div>\n              )}\n\n              <Button type=\"submit\" className=\"w-full\" size=\"lg\" disabled={submitting}>\n                {submitting ? (\n                  <>\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                    Submitting Vote...\n                  </>\n                ) : (\n                  <>\n                    <Vote className=\"h-5 w-5 mr-2\" />\n                    Submit My Vote\n                  </>\n                )}\n              </Button>\n            </form>\n          </CardContent>\n        </Card>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AATA;;;;;;;;;AAoBe,SAAS,SAAS,EAAE,MAAM,EAAuC;;IAC9E,MAAM,EAAE,IAAI,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW,EAAE;QACb,aAAa;QACb,MAAM;QACN,iBAAiB;IACnB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM;gDAAY;oBAChB,MAAM,iBAAiB,MAAM;oBAC7B,UAAU,eAAe,EAAE;gBAC7B;;YACA;QACF;6BAAG;QAAC;KAAO;IAEX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,QAAQ,QAAQ;gBAClB;YACF;QACF;6BAAG;QAAC;QAAM;KAAO;IAEjB,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,QAAQ;YACnD,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,SAAS,KAAK,KAAK,IAAI;gBACvB;YACF;YAEA,QAAQ,KAAK,IAAI;YAEjB,0CAA0C;YAC1C,IAAI,KAAK,IAAI,CAAC,WAAW,IAAI,KAAK,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE;gBAC5D,iBAAiB,KAAK,IAAI,CAAC,WAAW,CAAC,SAAS;YAClD;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,WAAW,KAAK,SAAS,CAAC,QAAQ,CAAC,YAC/B,KAAK,SAAS,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM,YACjC;uBAAI,KAAK,SAAS;oBAAE;iBAAS;YACnC,CAAC;IACH;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;IACjD;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,cAAc;QACd,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,cAAc;gBACzC,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,WAAW,SAAS,SAAS;oBAC7B,aAAa,SAAS,WAAW;oBACjC,MAAM,SAAS,IAAI;oBACnB,iBAAiB,SAAS,eAAe;gBAC3C;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,SAAS,KAAK,KAAK,IAAI;gBACvB;YACF;YAEA,WAAW;QACb,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,cAAc;QAChB;IACF;IAEA,IAAI,eAAe,SAAS;QAC1B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,CAAC,MAAM;QACT,OAAO,IAAI,CAAC;QACZ,OAAO;IACT;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;0CAAE;;;;;;;;;;;;kCAEpB,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;0CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMpB;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,6LAAC,8NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,MAAM,EAAE,QAAQ;0CAC3B,cAAA,6LAAC,qIAAA,CAAA,SAAM;8CAAC;;;;;;;;;;;0CAEV,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;8CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQzD;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;;sDAC3B,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAI1C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO1D,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CACjB,KAAK,KAAK;;;;;;;kDAEb,6LAAC,mIAAA,CAAA,kBAAe;;4CACb,KAAK,WAAW;4CAAC;4CAAI,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,UAAU;4CAAE;4CAAI,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,QAAQ;;;;;;;;;;;;;4BAGjF,KAAK,WAAW,kBACf,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAE,WAAU;8CAAiB,KAAK,WAAW;;;;;;;;;;;;;;;;;kCAMpD,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAK,UAAU;oCAAc,WAAU;;sDAEtC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAwB;;;;;;8DACtC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAM,WAAU;sEAAoC;;;;;;sEAGrD,6LAAC;4DAAI,WAAU;sEACZ,cAAc,GAAG,CAAC,CAAC,yBAClB,6LAAC;oEAEC,MAAK;oEACL,SAAS,IAAM,qBAAqB;oEACpC,WAAW,CAAC,sDAAsD,EAChE,SAAS,SAAS,CAAC,QAAQ,CAAC,YACxB,qDACA,iFACJ;8EAED;mEATI;;;;;;;;;;;;;;;;;;;;;;sDAiBf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,SAAQ;oDAAc,WAAU;8DAAoC;;;;;;8DAG3E,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,WAAW;oDAC3B,UAAU;oDACV,WAAU;;sEAEV,6LAAC;4DAAO,OAAM;sEAAS;;;;;;sEACvB,6LAAC;4DAAO,OAAM;sEAAY;;;;;;sEAC1B,6LAAC;4DAAO,OAAM;sEAAS;;;;;;;;;;;;;;;;;;sDAK3B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,SAAQ;oDAAO,WAAU;8DAAoC;;;;;;8DAGpE,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,IAAI;oDACpB,UAAU;oDACV,WAAU;;sEAEV,6LAAC;4DAAO,OAAM;sEAAU;;;;;;sEACxB,6LAAC;4DAAO,OAAM;sEAAW;;;;;;sEACzB,6LAAC;4DAAO,OAAM;sEAAS;;;;;;;;;;;;;;;;;;sDAK3B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,SAAQ;oDAAkB,WAAU;8DAAoC;;;;;;8DAG/E,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAM;oDACN,WAAU;oDACV,aAAY;oDACZ,OAAO,SAAS,eAAe;oDAC/B,UAAU;;;;;;;;;;;;wCAIb,uBACC,6LAAC;4CAAI,WAAU;sDACZ;;;;;;sDAIL,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAS,WAAU;4CAAS,MAAK;4CAAK,UAAU;sDAC1D,2BACC;;kEACE,6LAAC;wDAAI,WAAU;;;;;;oDAAuE;;6EAIxF;;kEACE,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWrD;GAtUwB;;QACiB,0HAAA,CAAA,UAAO;QAC/B,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}