{"version": 3, "file": "Tokenizer.d.ts", "sourceRoot": "https://raw.githubusercontent.com/fb55/htmlparser2/c123610e003a1eaebc61febed01cabb6e41eb658/src/", "sources": ["Tokenizer.ts"], "names": [], "mappings": "AAkHA,oBAAY,SAAS;IACjB,OAAO,IAAI;IACX,QAAQ,IAAI;IACZ,MAAM,IAAI;IACV,MAAM,IAAI;CACb;AAED,MAAM,WAAW,SAAS;IACtB,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IACpD,cAAc,CAAC,SAAS,EAAE,MAAM,GAAG,IAAI,CAAC;IACxC,WAAW,CAAC,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IACtD,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IACpD,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG,IAAI,CAAC;IAClE,UAAU,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IAClD,SAAS,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG,IAAI,CAAC;IACpE,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IACrD,KAAK,IAAI,IAAI,CAAC;IACd,YAAY,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IACrC,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IACrD,uBAAuB,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IAC/D,gBAAgB,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IACzC,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IAC9C,YAAY,CAAC,SAAS,EAAE,MAAM,GAAG,IAAI,CAAC;CACzC;AAiBD,MAAM,CAAC,OAAO,OAAO,SAAS;IA2BtB,OAAO,CAAC,QAAQ,CAAC,GAAG;IA1BxB,6CAA6C;IAC7C,OAAO,CAAC,KAAK,CAAc;IAC3B,uBAAuB;IACvB,OAAO,CAAC,MAAM,CAAM;IACpB,iEAAiE;IACjE,OAAO,CAAC,YAAY,CAAK;IACzB,oEAAoE;IACpE,OAAO,CAAC,KAAK,CAAK;IAClB,kIAAkI;IAClI,OAAO,CAAC,SAAS,CAAc;IAC/B,oEAAoE;IACpE,OAAO,CAAC,SAAS,CAAS;IAC1B,uDAAuD;IAChD,OAAO,UAAQ;IACtB,wCAAwC;IACxC,OAAO,CAAC,MAAM,CAAK;IAEnB,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAU;IAClC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAU;IACzC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAc;gBAGrC,EACI,OAAe,EACf,cAAqB,GACxB,EAAE;QAAE,OAAO,CAAC,EAAE,OAAO,CAAC;QAAC,cAAc,CAAC,EAAE,OAAO,CAAA;KAAE,EACjC,GAAG,EAAE,SAAS;IAO5B,KAAK,IAAI,IAAI;IAWb,KAAK,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI;IAM1B,GAAG,IAAI,IAAI;IAIX,KAAK,IAAI,IAAI;IAIb,MAAM,IAAI,IAAI;IAOrB;;OAEG;IACI,QAAQ,IAAI,MAAM;IAIzB;;OAEG;IACI,eAAe,IAAI,MAAM;IAIhC,OAAO,CAAC,SAAS;IAejB,OAAO,CAAC,eAAe,CAA0B;IACjD,OAAO,CAAC,aAAa,CAAK;IAC1B,OAAO,CAAC,yBAAyB;IAoBjC,mEAAmE;IACnE,OAAO,CAAC,iBAAiB;IAwCzB,OAAO,CAAC,kBAAkB;IAe1B;;;;;OAKG;IACH,OAAO,CAAC,aAAa;IAkBrB;;;;;;;OAOG;IACH,OAAO,CAAC,kBAAkB;IAwB1B;;;;;OAKG;IACH,OAAO,CAAC,cAAc;IAItB,OAAO,CAAC,YAAY;IAOpB,OAAO,CAAC,kBAAkB;IAyB1B,OAAO,CAAC,cAAc;IAQtB,OAAO,CAAC,yBAAyB;IAYjC,OAAO,CAAC,qBAAqB;IAQ7B,OAAO,CAAC,wBAAwB;IAQhC,OAAO,CAAC,wBAAwB;IAkBhC,OAAO,CAAC,qBAAqB;IAY7B,OAAO,CAAC,oBAAoB;IAQ5B,OAAO,CAAC,uBAAuB;IAa/B,OAAO,CAAC,yBAAyB;IAajC,OAAO,CAAC,sBAAsB;IAmB9B,OAAO,CAAC,iCAAiC;IAGzC,OAAO,CAAC,iCAAiC;IAGzC,OAAO,CAAC,6BAA6B;IAYrC,OAAO,CAAC,sBAAsB;IAW9B,OAAO,CAAC,kBAAkB;IAO1B,OAAO,CAAC,4BAA4B;IAOpC,OAAO,CAAC,kBAAkB;IAW1B,OAAO,CAAC,qBAAqB;IAO7B,OAAO,CAAC,mBAAmB;IAY3B,OAAO,CAAC,SAAS,CAAK;IACtB,OAAO,CAAC,WAAW,CAAK;IACxB,wFAAwF;IACxF,OAAO,CAAC,YAAY,CAAK;IACzB,OAAO,CAAC,YAAY,CAAK;IAEzB,OAAO,CAAC,iBAAiB;IAiBzB,OAAO,CAAC,kBAAkB;IAiD1B,OAAO,CAAC,eAAe;IA8BvB,OAAO,CAAC,wBAAwB;IAUhC,OAAO,CAAC,iBAAiB;IAgBzB,OAAO,CAAC,oBAAoB;IAe5B,OAAO,CAAC,gBAAgB;IAoBxB,OAAO,CAAC,iBAAiB;IAQzB;;OAEG;IACH,OAAO,CAAC,OAAO;IAoBf,OAAO,CAAC,cAAc;IAItB;;;;OAIG;IACH,OAAO,CAAC,KAAK;IA8Hb,OAAO,CAAC,MAAM;IAYd,gCAAgC;IAChC,OAAO,CAAC,kBAAkB;IAwC1B,OAAO,CAAC,WAAW;IAUnB,OAAO,CAAC,aAAa;CAUxB"}