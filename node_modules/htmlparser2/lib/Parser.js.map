{"version": 3, "file": "Parser.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/htmlparser2/c123610e003a1eaebc61febed01cabb6e41eb658/src/", "sources": ["Parser.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6DAAiE;AACjE,oDAAuD;AAEvD,IAAM,QAAQ,GAAG,IAAI,GAAG,CAAC;IACrB,OAAO;IACP,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,UAAU;IACV,UAAU;CACb,CAAC,CAAC;AACH,IAAM,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5B,IAAM,gBAAgB,GAAG,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;AACrD,IAAM,OAAO,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AACtC,IAAM,OAAO,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AAEtC,IAAM,gBAAgB,GAAG,IAAI,GAAG,CAAsB;IAClD,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;IACnC,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IACvB,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;IACtC,CAAC,MAAM,EAAE,IAAI,GAAG,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;IAC7C,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IACvB,CAAC,GAAG,EAAE,IAAI,CAAC;IACX,CAAC,IAAI,EAAE,IAAI,CAAC;IACZ,CAAC,IAAI,EAAE,IAAI,CAAC;IACZ,CAAC,IAAI,EAAE,IAAI,CAAC;IACZ,CAAC,IAAI,EAAE,IAAI,CAAC;IACZ,CAAC,IAAI,EAAE,IAAI,CAAC;IACZ,CAAC,IAAI,EAAE,IAAI,CAAC;IACZ,CAAC,QAAQ,EAAE,QAAQ,CAAC;IACpB,CAAC,OAAO,EAAE,QAAQ,CAAC;IACnB,CAAC,QAAQ,EAAE,QAAQ,CAAC;IACpB,CAAC,QAAQ,EAAE,QAAQ,CAAC;IACpB,CAAC,UAAU,EAAE,QAAQ,CAAC;IACtB,CAAC,UAAU,EAAE,QAAQ,CAAC;IACtB,CAAC,QAAQ,EAAE,IAAI,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC/B,CAAC,UAAU,EAAE,IAAI,GAAG,CAAC,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC;IAC7C,CAAC,IAAI,EAAE,OAAO,CAAC;IACf,CAAC,IAAI,EAAE,OAAO,CAAC;IACf,CAAC,SAAS,EAAE,IAAI,CAAC;IACjB,CAAC,SAAS,EAAE,IAAI,CAAC;IACjB,CAAC,OAAO,EAAE,IAAI,CAAC;IACf,CAAC,YAAY,EAAE,IAAI,CAAC;IACpB,CAAC,SAAS,EAAE,IAAI,CAAC;IACjB,CAAC,KAAK,EAAE,IAAI,CAAC;IACb,CAAC,IAAI,EAAE,IAAI,CAAC;IACZ,CAAC,UAAU,EAAE,IAAI,CAAC;IAClB,CAAC,YAAY,EAAE,IAAI,CAAC;IACpB,CAAC,QAAQ,EAAE,IAAI,CAAC;IAChB,CAAC,QAAQ,EAAE,IAAI,CAAC;IAChB,CAAC,MAAM,EAAE,IAAI,CAAC;IACd,CAAC,QAAQ,EAAE,IAAI,CAAC;IAChB,CAAC,IAAI,EAAE,IAAI,CAAC;IACZ,CAAC,MAAM,EAAE,IAAI,CAAC;IACd,CAAC,KAAK,EAAE,IAAI,CAAC;IACb,CAAC,IAAI,EAAE,IAAI,CAAC;IACZ,CAAC,KAAK,EAAE,IAAI,CAAC;IACb,CAAC,SAAS,EAAE,IAAI,CAAC;IACjB,CAAC,OAAO,EAAE,IAAI,CAAC;IACf,CAAC,IAAI,EAAE,IAAI,CAAC;IACZ,CAAC,IAAI,EAAE,OAAO,CAAC;IACf,CAAC,IAAI,EAAE,OAAO,CAAC;IACf,CAAC,OAAO,EAAE,gBAAgB,CAAC;IAC3B,CAAC,OAAO,EAAE,gBAAgB,CAAC;CAC9B,CAAC,CAAC;AAEH,IAAM,YAAY,GAAG,IAAI,GAAG,CAAC;IACzB,MAAM;IACN,MAAM;IACN,UAAU;IACV,IAAI;IACJ,KAAK;IACL,SAAS;IACT,OAAO;IACP,OAAO;IACP,IAAI;IACJ,KAAK;IACL,OAAO;IACP,SAAS;IACT,QAAQ;IACR,MAAM;IACN,MAAM;IACN,OAAO;IACP,QAAQ;IACR,OAAO;IACP,KAAK;CACR,CAAC,CAAC;AAEH,IAAM,sBAAsB,GAAG,IAAI,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;AAExD,IAAM,uBAAuB,GAAG,IAAI,GAAG,CAAC;IACpC,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,gBAAgB;IAChB,eAAe;IACf,MAAM;IACN,OAAO;CACV,CAAC,CAAC;AA+FH,IAAM,SAAS,GAAG,OAAO,CAAC;AAE1B;IA6BI,gBACI,GAA6B,EACZ,OAA2B;QAA3B,wBAAA,EAAA,YAA2B;;QAA3B,YAAO,GAAP,OAAO,CAAoB;QA9BhD,yCAAyC;QAClC,eAAU,GAAG,CAAC,CAAC;QACtB,uCAAuC;QAChC,aAAQ,GAAG,CAAC,CAAC;QACpB;;;WAGG;QACK,iBAAY,GAAG,CAAC,CAAC;QAEjB,YAAO,GAAG,EAAE,CAAC;QACb,eAAU,GAAG,EAAE,CAAC;QAChB,gBAAW,GAAG,EAAE,CAAC;QACjB,YAAO,GAAqC,IAAI,CAAC;QACxC,UAAK,GAAa,EAAE,CAAC;QACrB,mBAAc,GAAc,EAAE,CAAC;QAM/B,YAAO,GAAa,EAAE,CAAC;QAChC,iBAAY,GAAG,CAAC,CAAC;QACzB,kFAAkF;QAC1E,eAAU,GAAG,CAAC,CAAC;QACvB,kFAAkF;QAC1E,UAAK,GAAG,KAAK,CAAC;QAMlB,IAAI,CAAC,GAAG,GAAG,GAAG,aAAH,GAAG,cAAH,GAAG,GAAI,EAAE,CAAC;QACrB,IAAI,CAAC,iBAAiB,GAAG,MAAA,OAAO,CAAC,aAAa,mCAAI,CAAC,OAAO,CAAC,OAAO,CAAC;QACnE,IAAI,CAAC,uBAAuB;YACxB,MAAA,OAAO,CAAC,uBAAuB,mCAAI,CAAC,OAAO,CAAC,OAAO,CAAC;QACxD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAA,OAAO,CAAC,SAAS,mCAAI,sBAAS,CAAC,CACjD,IAAI,CAAC,OAAO,EACZ,IAAI,CACP,CAAC;QACF,MAAA,MAAA,IAAI,CAAC,GAAG,EAAC,YAAY,mDAAG,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,2BAA2B;IAE3B,gBAAgB;IAChB,uBAAM,GAAN,UAAO,KAAa,EAAE,QAAgB;;QAClC,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAC5C,IAAI,CAAC,QAAQ,GAAG,QAAQ,GAAG,CAAC,CAAC;QAC7B,MAAA,MAAA,IAAI,CAAC,GAAG,EAAC,MAAM,mDAAG,IAAI,CAAC,CAAC;QACxB,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC;IAC/B,CAAC;IAED,gBAAgB;IAChB,6BAAY,GAAZ,UAAa,EAAU;;QACnB;;;WAGG;QACH,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC;QAC/C,IAAI,CAAC,QAAQ,GAAG,KAAK,GAAG,CAAC,CAAC;QAC1B,MAAA,MAAA,IAAI,CAAC,GAAG,EAAC,MAAM,mDAAG,IAAA,yBAAa,EAAC,EAAE,CAAC,CAAC,CAAC;QACrC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;IAC5B,CAAC;IAES,8BAAa,GAAvB,UAAwB,IAAY;QAChC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC3D,CAAC;IAED,gBAAgB;IAChB,8BAAa,GAAb,UAAc,KAAa,EAAE,QAAgB;QACzC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAEzB,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAE1C,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;SAC7B;QAED,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;IAEO,4BAAW,GAAnB,UAAoB,IAAY;;QAC5B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC;QACpC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QAEpB,IAAM,YAAY,GACd,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAExD,IAAI,YAAY,EAAE;YACd,OACI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC;gBACrB,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,EACrD;gBACE,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,EAAG,CAAC;gBAClC,MAAA,MAAA,IAAI,CAAC,GAAG,EAAC,UAAU,mDAAG,OAAO,EAAE,IAAI,CAAC,CAAC;aACxC;SACJ;QACD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;YAC3B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtB,IAAI,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBAClC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAClC;iBAAM,IAAI,uBAAuB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBAC1C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACnC;SACJ;QACD,MAAA,MAAA,IAAI,CAAC,GAAG,EAAC,aAAa,mDAAG,IAAI,CAAC,CAAC;QAC/B,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS;YAAE,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;IAC9C,CAAC;IAEO,2BAAU,GAAlB,UAAmB,SAAkB;;QACjC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC;QAEpC,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,MAAA,MAAA,IAAI,CAAC,GAAG,EAAC,SAAS,mDAAG,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;YAC5D,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;SACvB;QACD,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YACzD,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;SAC3C;QAED,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;IACtB,CAAC;IAED,gBAAgB;IAChB,6BAAY,GAAZ,UAAa,QAAgB;QACzB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAEvB,iCAAiC;QACjC,IAAI,CAAC,UAAU,GAAG,QAAQ,GAAG,CAAC,CAAC;IACnC,CAAC;IAED,gBAAgB;IAChB,2BAAU,GAAV,UAAW,KAAa,EAAE,QAAgB;;QACtC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAEzB,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAE1C,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;SAC7B;QAED,IACI,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC;YAChC,uBAAuB,CAAC,GAAG,CAAC,IAAI,CAAC,EACnC;YACE,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC;SAC7B;QAED,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;YAC3B,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACzC,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;gBACZ,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE;oBACrB,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;oBACpC,OAAO,KAAK,EAAE,EAAE;wBACZ,6CAA6C;wBAC7C,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAG,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC;qBACvD;iBACJ;;oBAAM,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;aAClC;iBAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI,KAAK,GAAG,EAAE;gBAC9C,6BAA6B;gBAC7B,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBACtB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;aAC9B;SACJ;aAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI,KAAK,IAAI,EAAE;YAC/C,oFAAoF;YACpF,MAAA,MAAA,IAAI,CAAC,GAAG,EAAC,aAAa,mDAAG,IAAI,CAAC,CAAC;YAC/B,MAAA,MAAA,IAAI,CAAC,GAAG,EAAC,SAAS,mDAAG,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;YACrC,MAAA,MAAA,IAAI,CAAC,GAAG,EAAC,UAAU,mDAAG,IAAI,EAAE,KAAK,CAAC,CAAC;SACtC;QAED,iCAAiC;QACjC,IAAI,CAAC,UAAU,GAAG,QAAQ,GAAG,CAAC,CAAC;IACnC,CAAC;IAED,gBAAgB;IAChB,iCAAgB,GAAhB,UAAiB,QAAgB;QAC7B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IACI,IAAI,CAAC,OAAO,CAAC,OAAO;YACpB,IAAI,CAAC,OAAO,CAAC,oBAAoB;YACjC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,EACrD;YACE,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAE5B,iCAAiC;YACjC,IAAI,CAAC,UAAU,GAAG,QAAQ,GAAG,CAAC,CAAC;SAClC;aAAM;YACH,gDAAgD;YAChD,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;SAC/B;IACL,CAAC;IAEO,gCAAe,GAAvB,UAAwB,aAAsB;;QAC1C,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC;QAC1B,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;QAE/B,oDAAoD;QACpD,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;YAC5C,uEAAuE;YACvE,MAAA,MAAA,IAAI,CAAC,GAAG,EAAC,UAAU,mDAAG,IAAI,EAAE,CAAC,aAAa,CAAC,CAAC;YAC5C,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;SACpB;IACL,CAAC;IAED,gBAAgB;IAChB,6BAAY,GAAZ,UAAa,KAAa,EAAE,QAAgB;QACxC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAE5C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,uBAAuB;YAC1C,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE;YACpB,CAAC,CAAC,IAAI,CAAC;IACf,CAAC;IAED,gBAAgB;IAChB,6BAAY,GAAZ,UAAa,KAAa,EAAE,QAAgB;QACxC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IACvD,CAAC;IAED,gBAAgB;IAChB,+BAAc,GAAd,UAAe,EAAU;QACrB,IAAI,CAAC,WAAW,IAAI,IAAA,yBAAa,EAAC,EAAE,CAAC,CAAC;IAC1C,CAAC;IAED,gBAAgB;IAChB,4BAAW,GAAX,UAAY,KAAgB,EAAE,QAAgB;;QAC1C,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAEzB,MAAA,MAAA,IAAI,CAAC,GAAG,EAAC,WAAW,mDAChB,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,WAAW,EAChB,KAAK,KAAK,wBAAS,CAAC,MAAM;YACtB,CAAC,CAAC,GAAG;YACL,CAAC,CAAC,KAAK,KAAK,wBAAS,CAAC,MAAM;gBAC5B,CAAC,CAAC,GAAG;gBACL,CAAC,CAAC,KAAK,KAAK,wBAAS,CAAC,OAAO;oBAC7B,CAAC,CAAC,SAAS;oBACX,CAAC,CAAC,IAAI,CACb,CAAC;QAEF,IACI,IAAI,CAAC,OAAO;YACZ,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,EACtE;YACE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;SACpD;QACD,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;IAC1B,CAAC;IAEO,mCAAkB,GAA1B,UAA2B,KAAa;QACpC,IAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACtC,IAAI,IAAI,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAEtD,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;SAC7B;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,gBAAgB;IAChB,8BAAa,GAAb,UAAc,KAAa,EAAE,QAAgB;QACzC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAE7C,IAAI,IAAI,CAAC,GAAG,CAAC,uBAAuB,EAAE;YAClC,IAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAC5C,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC,WAAI,IAAI,CAAE,EAAE,WAAI,KAAK,CAAE,CAAC,CAAC;SAC7D;QAED,iCAAiC;QACjC,IAAI,CAAC,UAAU,GAAG,QAAQ,GAAG,CAAC,CAAC;IACnC,CAAC;IAED,gBAAgB;IAChB,wCAAuB,GAAvB,UAAwB,KAAa,EAAE,QAAgB;QACnD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAE7C,IAAI,IAAI,CAAC,GAAG,CAAC,uBAAuB,EAAE;YAClC,IAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAC5C,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC,WAAI,IAAI,CAAE,EAAE,WAAI,KAAK,CAAE,CAAC,CAAC;SAC7D;QAED,iCAAiC;QACjC,IAAI,CAAC,UAAU,GAAG,QAAQ,GAAG,CAAC,CAAC;IACnC,CAAC;IAED,gBAAgB;IAChB,0BAAS,GAAT,UAAU,KAAa,EAAE,QAAgB,EAAE,MAAc;;QACrD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAEzB,MAAA,MAAA,IAAI,CAAC,GAAG,EAAC,SAAS,mDAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC;QAC9D,MAAA,MAAA,IAAI,CAAC,GAAG,EAAC,YAAY,kDAAI,CAAC;QAE1B,iCAAiC;QACjC,IAAI,CAAC,UAAU,GAAG,QAAQ,GAAG,CAAC,CAAC;IACnC,CAAC;IAED,gBAAgB;IAChB,wBAAO,GAAP,UAAQ,KAAa,EAAE,QAAgB,EAAE,MAAc;;QACnD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,GAAG,MAAM,CAAC,CAAC;QAEtD,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;YACrD,MAAA,MAAA,IAAI,CAAC,GAAG,EAAC,YAAY,kDAAI,CAAC;YAC1B,MAAA,MAAA,IAAI,CAAC,GAAG,EAAC,MAAM,mDAAG,KAAK,CAAC,CAAC;YACzB,MAAA,MAAA,IAAI,CAAC,GAAG,EAAC,UAAU,kDAAI,CAAC;SAC3B;aAAM;YACH,MAAA,MAAA,IAAI,CAAC,GAAG,EAAC,SAAS,mDAAG,iBAAU,KAAK,OAAI,CAAC,CAAC;YAC1C,MAAA,MAAA,IAAI,CAAC,GAAG,EAAC,YAAY,kDAAI,CAAC;SAC7B;QAED,iCAAiC;QACjC,IAAI,CAAC,UAAU,GAAG,QAAQ,GAAG,CAAC,CAAC;IACnC,CAAC;IAED,gBAAgB;IAChB,sBAAK,GAAL;;QACI,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE;YACrB,2CAA2C;YAC3C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC;YAChC,KACI,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAC7B,KAAK,GAAG,CAAC,EACT,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC;gBACjD,CAAC;SACL;QACD,MAAA,MAAA,IAAI,CAAC,GAAG,EAAC,KAAK,kDAAI,CAAC;IACvB,CAAC;IAED;;OAEG;IACI,sBAAK,GAAZ;;QACI,MAAA,MAAA,IAAI,CAAC,GAAG,EAAC,OAAO,kDAAI,CAAC;QACrB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,MAAA,MAAA,IAAI,CAAC,GAAG,EAAC,YAAY,mDAAG,IAAI,CAAC,CAAC;QAC9B,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;QACxB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACvB,CAAC;IAED;;;;;OAKG;IACI,8BAAa,GAApB,UAAqB,IAAY;QAC7B,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACnB,CAAC;IAEO,yBAAQ,GAAhB,UAAiB,KAAa,EAAE,GAAW;QACvC,OAAO,KAAK,GAAG,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;YACxD,IAAI,CAAC,WAAW,EAAE,CAAC;SACtB;QAED,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAC7B,KAAK,GAAG,IAAI,CAAC,YAAY,EACzB,GAAG,GAAG,IAAI,CAAC,YAAY,CAC1B,CAAC;QAEF,OAAO,GAAG,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;YACrD,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;SAC9D;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,4BAAW,GAAnB;QACI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAC5C,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;IACzB,CAAC;IAED;;;;OAIG;IACI,sBAAK,GAAZ,UAAa,KAAa;;QACtB,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,MAAA,MAAA,IAAI,CAAC,GAAG,EAAC,OAAO,mDAAG,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC;YACtD,OAAO;SACV;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzB,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;YACxB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC5B,IAAI,CAAC,UAAU,EAAE,CAAC;SACrB;IACL,CAAC;IAED;;;;OAIG;IACI,oBAAG,GAAV,UAAW,KAAc;;QACrB,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,MAAA,MAAA,IAAI,CAAC,GAAG,EAAC,OAAO,mDAAG,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC;YACpD,OAAO;SACV;QAED,IAAI,KAAK;YAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG;IACI,sBAAK,GAAZ;QACI,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACI,uBAAM,GAAb;QACI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;QAExB,OACI,IAAI,CAAC,SAAS,CAAC,OAAO;YACtB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EACvC;YACE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;SACzD;QAED,IAAI,IAAI,CAAC,KAAK;YAAE,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;IACzC,CAAC;IAED;;;;;OAKG;IACI,2BAAU,GAAjB,UAAkB,KAAa;QAC3B,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACtB,CAAC;IACD;;;;;OAKG;IACI,qBAAI,GAAX,UAAY,KAAc;QACtB,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACpB,CAAC;IACL,aAAC;AAAD,CAAC,AA/cD,IA+cC;AA/cY,wBAAM"}