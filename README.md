# Travel Planner - AI-Powered Itinerary Generator

A modern travel planning application built with Next.js, Supabase, and OpenAI that creates personalized travel itineraries using AI.

## Features

- 🤖 **AI-Powered Itinerary Generation** - Create detailed day-by-day itineraries using OpenAI GPT-4
- 🔐 **User Authentication** - Secure authentication with Supabase Auth
- 📧 **Email Invitations** - Send beautiful HTML email invitations to travel companions
- 🗳️ **Collaborative Voting** - Invited users vote on trip preferences before itinerary generation
- 📱 **Responsive Design** - Beautiful, mobile-first design with Tailwind CSS
- 📄 **PDF Export** - Download itineraries as formatted PDF documents
- 💾 **Data Persistence** - Store trips and activities in Supabase PostgreSQL
- 🎨 **Modern UI** - Clean interface with Radix UI components
- ⚡ **Real-time Updates** - Live data synchronization

## Tech Stack

- **Frontend**: Next.js 14 (App Router), React, TypeScript
- **Styling**: Tailwind CSS, Radix UI
- **Backend**: Next.js API Routes
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **AI**: OpenAI GPT-4 API
- **PDF Generation**: jsPDF
- **Icons**: Lucide React

## Prerequisites

Before you begin, ensure you have:

- Node.js 18+ installed
- A Supabase account and project
- An OpenAI API key

## Setup Instructions

### 1. Clone the Repository

```bash
git clone <your-repo-url>
cd travel-planner
npm install
```

### 2. Set Up Supabase

1. Create a new project at [supabase.com](https://supabase.com)
2. Go to Settings > API to get your project URL and anon key
3. Go to Settings > Database and run the SQL from `supabase-schema.sql`
4. Enable Row Level Security (RLS) is already configured in the schema

### 3. Set Up OpenAI

1. Get your API key from [OpenAI](https://platform.openai.com/api-keys)
2. Make sure you have access to GPT-4 (or modify the model in `src/lib/openai.ts`)

### 4. Set Up Email Service (Resend)

1. Sign up at [Resend](https://resend.com)
2. Get your API key from the dashboard
3. (Optional) Verify your domain for production use

### 5. Environment Variables

Copy `.env.example` to `.env.local` and fill in your values:

```bash
cp .env.example .env.local
```

Update `.env.local` with your actual values:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key

# Email Configuration (Resend)
RESEND_API_KEY=your_resend_api_key

# Next.js Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### 6. Run the Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── api/               # API routes
│   ├── auth/              # Authentication pages
│   ├── dashboard/         # User dashboard
│   ├── trip/              # Trip-related pages
│   └── globals.css        # Global styles
├── components/            # React components
│   └── ui/               # Reusable UI components
├── hooks/                # Custom React hooks
├── lib/                  # Utility libraries
│   ├── supabase.ts       # Supabase client configuration
│   ├── openai.ts         # OpenAI integration
│   ├── database.types.ts # TypeScript types
│   ├── utils.ts          # Utility functions
│   └── pdf-generator.ts  # PDF generation
└── middleware.ts         # Next.js middleware for auth
```

## Key Features Walkthrough

### 1. User Authentication
- Sign up/Sign in with email and password
- Automatic profile creation
- Protected routes with middleware
- Session management with Supabase Auth

### 2. Trip Creation
- Form-based trip creation with preferences
- Date validation and budget tracking
- Interest selection and travel pace options

### 3. AI Itinerary Generation
- Integration with OpenAI GPT-4
- Personalized recommendations based on preferences
- Structured itinerary with activities, times, and costs
- Automatic activity creation in database

### 4. Trip Management
- Dashboard with trip overview
- Individual trip pages with detailed itineraries
- Edit and update trip information

### 5. PDF Export
- Generate formatted PDF itineraries
- Include all trip details and daily activities
- Professional layout with proper formatting

## Database Schema

The application uses the following main tables:

- **profiles**: User profile information
- **trips**: Trip details and preferences
- **activities**: Individual activities within trips

See `supabase-schema.sql` for the complete schema with RLS policies.

## API Routes

- `GET/POST /api/trips` - Trip CRUD operations
- `POST /api/ai/generate-itinerary` - AI itinerary generation
- `POST /api/pdf/export` - PDF export functionality

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support, please open an issue on GitHub or contact the development team.
