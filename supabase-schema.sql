-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create profiles table
CREATE TABLE IF NOT EXISTS profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create trips table
CREATE TABLE IF NOT EXISTS trips (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  destination TEXT NOT NULL,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  budget DECIMAL(10,2),
  preferences JSONB,
  itinerary JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create activities table
CREATE TABLE IF NOT EXISTS activities (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  trip_id UUID REFERENCES trips(id) ON DELETE CASCADE NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  location TEXT,
  date DATE NOT NULL,
  start_time TIME,
  end_time TIME,
  cost DECIMAL(10,2),
  category TEXT,
  booking_url TEXT,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create trip_invitations table
CREATE TABLE IF NOT EXISTS trip_invitations (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  trip_id UUID REFERENCES trips(id) ON DELETE CASCADE NOT NULL,
  inviter_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  email TEXT NOT NULL,
  invited_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined')),
  token TEXT UNIQUE NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create trip_votes table
CREATE TABLE IF NOT EXISTS trip_votes (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  trip_id UUID REFERENCES trips(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  interests TEXT[] DEFAULT '{}',
  budget_level TEXT CHECK (budget_level IN ('budget', 'mid-range', 'luxury')),
  pace TEXT CHECK (pace IN ('relaxed', 'moderate', 'packed')),
  additional_notes TEXT,
  voted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(trip_id, user_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS trips_user_id_idx ON trips(user_id);
CREATE INDEX IF NOT EXISTS trips_start_date_idx ON trips(start_date);
CREATE INDEX IF NOT EXISTS activities_trip_id_idx ON activities(trip_id);
CREATE INDEX IF NOT EXISTS activities_date_idx ON activities(date);
CREATE INDEX IF NOT EXISTS trip_invitations_trip_id_idx ON trip_invitations(trip_id);
CREATE INDEX IF NOT EXISTS trip_invitations_email_idx ON trip_invitations(email);
CREATE INDEX IF NOT EXISTS trip_invitations_token_idx ON trip_invitations(token);
CREATE INDEX IF NOT EXISTS trip_votes_trip_id_idx ON trip_votes(trip_id);
CREATE INDEX IF NOT EXISTS trip_votes_user_id_idx ON trip_votes(user_id);

-- Set up Row Level Security (RLS)
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE trips ENABLE ROW LEVEL SECURITY;
ALTER TABLE activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE trip_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE trip_votes ENABLE ROW LEVEL SECURITY;

-- Create policies for profiles
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Create policies for trips
CREATE POLICY "Users can view own trips" ON trips
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own trips" ON trips
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own trips" ON trips
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own trips" ON trips
  FOR DELETE USING (auth.uid() = user_id);

-- Create policies for activities
CREATE POLICY "Users can view activities for own trips" ON activities
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM trips 
      WHERE trips.id = activities.trip_id 
      AND trips.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create activities for own trips" ON activities
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM trips 
      WHERE trips.id = activities.trip_id 
      AND trips.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update activities for own trips" ON activities
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM trips 
      WHERE trips.id = activities.trip_id 
      AND trips.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete activities for own trips" ON activities
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM trips 
      WHERE trips.id = activities.trip_id 
      AND trips.user_id = auth.uid()
    )
  );

-- Function to handle user profile creation
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO profiles (id, email, full_name)
  VALUES (
    NEW.id,
    NEW.email,
    NEW.raw_user_meta_data->>'full_name'
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically create profile when user signs up
CREATE OR REPLACE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers to update updated_at timestamp
CREATE TRIGGER update_profiles_updated_at
  BEFORE UPDATE ON profiles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_trips_updated_at
  BEFORE UPDATE ON trips
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_activities_updated_at
  BEFORE UPDATE ON activities
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Policies for trip_invitations
CREATE POLICY "Users can view invitations for their trips" ON trip_invitations
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM trips
      WHERE trips.id = trip_invitations.trip_id
      AND trips.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can view their own invitations" ON trip_invitations
  FOR SELECT USING (
    invited_user_id = auth.uid() OR
    email = (SELECT email FROM auth.users WHERE id = auth.uid())
  );

CREATE POLICY "Users can create invitations for their trips" ON trip_invitations
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM trips
      WHERE trips.id = trip_invitations.trip_id
      AND trips.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update invitations for their trips" ON trip_invitations
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM trips
      WHERE trips.id = trip_invitations.trip_id
      AND trips.user_id = auth.uid()
    ) OR invited_user_id = auth.uid()
  );

-- Policies for trip_votes
CREATE POLICY "Users can view votes for trips they're invited to" ON trip_votes
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM trips
      WHERE trips.id = trip_votes.trip_id
      AND (trips.user_id = auth.uid() OR
           EXISTS (
             SELECT 1 FROM trip_invitations
             WHERE trip_invitations.trip_id = trips.id
             AND trip_invitations.invited_user_id = auth.uid()
             AND trip_invitations.status = 'accepted'
           ))
    )
  );

CREATE POLICY "Users can create votes for trips they're invited to" ON trip_votes
  FOR INSERT WITH CHECK (
    user_id = auth.uid() AND
    EXISTS (
      SELECT 1 FROM trips
      WHERE trips.id = trip_votes.trip_id
      AND (trips.user_id = auth.uid() OR
           EXISTS (
             SELECT 1 FROM trip_invitations
             WHERE trip_invitations.trip_id = trips.id
             AND trip_invitations.invited_user_id = auth.uid()
             AND trip_invitations.status = 'accepted'
           ))
    )
  );

CREATE POLICY "Users can update their own votes" ON trip_votes
  FOR UPDATE USING (user_id = auth.uid());

-- Triggers for new tables
CREATE TRIGGER update_trip_invitations_updated_at
  BEFORE UPDATE ON trip_invitations
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
