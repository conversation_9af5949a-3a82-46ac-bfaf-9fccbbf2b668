export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      trips: {
        Row: {
          id: string
          user_id: string
          title: string
          description: string | null
          destination: string
          start_date: string
          end_date: string
          budget: number | null
          preferences: Json | null
          itinerary: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          description?: string | null
          destination: string
          start_date: string
          end_date: string
          budget?: number | null
          preferences?: Json | null
          itinerary?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          description?: string | null
          destination?: string
          start_date?: string
          end_date?: string
          budget?: number | null
          preferences?: Json | null
          itinerary?: Json | null
          created_at?: string
          updated_at?: string
        }
      }
      activities: {
        Row: {
          id: string
          trip_id: string
          title: string
          description: string | null
          location: string | null
          date: string
          start_time: string | null
          end_time: string | null
          cost: number | null
          category: string | null
          booking_url: string | null
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          trip_id: string
          title: string
          description?: string | null
          location?: string | null
          date: string
          start_time?: string | null
          end_time?: string | null
          cost?: number | null
          category?: string | null
          booking_url?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          trip_id?: string
          title?: string
          description?: string | null
          location?: string | null
          date?: string
          start_time?: string | null
          end_time?: string | null
          cost?: number | null
          category?: string | null
          booking_url?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

// Helper types
export type Profile = Database['public']['Tables']['profiles']['Row']
export type Trip = Database['public']['Tables']['trips']['Row']
export type Activity = Database['public']['Tables']['activities']['Row']

export type TripWithActivities = Trip & {
  activities: Activity[]
}

export type ItineraryDay = {
  date: string
  activities: Activity[]
}

export type TripPreferences = {
  interests: string[]
  budget_level: 'budget' | 'mid-range' | 'luxury'
  pace: 'relaxed' | 'moderate' | 'packed'
  accommodation_type: string[]
  transportation: string[]
  dietary_restrictions: string[]
}
