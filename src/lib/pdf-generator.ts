import jsPDF from 'jspdf'
import { Trip, Activity } from './database.types'
import { formatDate, formatCurrency } from './utils'

export const generateTripPDF = (trip: Trip, activities: Activity[]) => {
  const pdf = new jsPDF()
  const pageWidth = pdf.internal.pageSize.getWidth()
  const pageHeight = pdf.internal.pageSize.getHeight()
  let yPosition = 20

  // Helper function to add text with word wrapping
  const addText = (text: string, x: number, y: number, maxWidth: number, fontSize = 12) => {
    pdf.setFontSize(fontSize)
    const lines = pdf.splitTextToSize(text, maxWidth)
    pdf.text(lines, x, y)
    return y + (lines.length * fontSize * 0.4)
  }

  // Header
  pdf.setFontSize(24)
  pdf.setFont('helvetica', 'bold')
  pdf.text(trip.title, 20, yPosition)
  yPosition += 15

  pdf.setFontSize(16)
  pdf.setFont('helvetica', 'normal')
  pdf.text(`${trip.destination}`, 20, yPosition)
  yPosition += 10

  pdf.setFontSize(12)
  pdf.text(`${formatDate(trip.start_date)} - ${formatDate(trip.end_date)}`, 20, yPosition)
  yPosition += 15

  // Trip Description
  if (trip.description) {
    pdf.setFont('helvetica', 'bold')
    pdf.text('Description:', 20, yPosition)
    yPosition += 8
    pdf.setFont('helvetica', 'normal')
    yPosition = addText(trip.description, 20, yPosition, pageWidth - 40)
    yPosition += 10
  }

  // Budget
  if (trip.budget) {
    pdf.setFont('helvetica', 'bold')
    pdf.text(`Budget: ${formatCurrency(trip.budget)}`, 20, yPosition)
    yPosition += 15
  }

  // Group activities by date
  const activitiesByDate = activities.reduce((acc, activity) => {
    const date = activity.date
    if (!acc[date]) {
      acc[date] = []
    }
    acc[date].push(activity)
    return acc
  }, {} as Record<string, Activity[]>)

  // Itinerary
  pdf.setFontSize(18)
  pdf.setFont('helvetica', 'bold')
  pdf.text('Itinerary', 20, yPosition)
  yPosition += 15

  Object.entries(activitiesByDate).forEach(([date, dayActivities]) => {
    // Check if we need a new page
    if (yPosition > pageHeight - 60) {
      pdf.addPage()
      yPosition = 20
    }

    // Date header
    pdf.setFontSize(14)
    pdf.setFont('helvetica', 'bold')
    pdf.text(formatDate(date), 20, yPosition)
    yPosition += 10

    // Activities for the day
    dayActivities.forEach((activity) => {
      // Check if we need a new page
      if (yPosition > pageHeight - 40) {
        pdf.addPage()
        yPosition = 20
      }

      pdf.setFontSize(12)
      pdf.setFont('helvetica', 'bold')
      
      // Activity title with time
      let activityHeader = activity.title
      if (activity.start_time) {
        activityHeader = `${activity.start_time} - ${activityHeader}`
      }
      pdf.text(activityHeader, 30, yPosition)
      yPosition += 6

      pdf.setFont('helvetica', 'normal')
      
      // Location
      if (activity.location) {
        pdf.text(`📍 ${activity.location}`, 30, yPosition)
        yPosition += 6
      }

      // Description
      if (activity.description) {
        yPosition = addText(activity.description, 30, yPosition, pageWidth - 60, 10)
        yPosition += 3
      }

      // Cost
      if (activity.cost) {
        pdf.text(`💰 ${formatCurrency(activity.cost)}`, 30, yPosition)
        yPosition += 6
      }

      // Notes
      if (activity.notes) {
        pdf.setFont('helvetica', 'italic')
        yPosition = addText(`Note: ${activity.notes}`, 30, yPosition, pageWidth - 60, 10)
        pdf.setFont('helvetica', 'normal')
        yPosition += 3
      }

      yPosition += 8
    })

    yPosition += 5
  })

  // Footer
  const totalPages = pdf.getNumberOfPages()
  for (let i = 1; i <= totalPages; i++) {
    pdf.setPage(i)
    pdf.setFontSize(10)
    pdf.setFont('helvetica', 'normal')
    pdf.text(
      `Generated by Travel Planner - Page ${i} of ${totalPages}`,
      pageWidth / 2,
      pageHeight - 10,
      { align: 'center' }
    )
  }

  return pdf
}

export const downloadTripPDF = (trip: Trip, activities: Activity[]) => {
  const pdf = generateTripPDF(trip, activities)
  const fileName = `${trip.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_itinerary.pdf`
  pdf.save(fileName)
}
