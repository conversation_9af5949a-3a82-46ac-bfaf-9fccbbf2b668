import { Resend } from 'resend'

if (!process.env.RESEND_API_KEY) {
  console.warn('RESEND_API_KEY is not set. Email functionality will be disabled.')
}

const resend = new Resend(process.env.RESEND_API_KEY)

interface InvitationEmailData {
  to: string
  inviterName: string
  inviterEmail: string
  tripTitle: string
  tripDescription?: string
  destination: string
  startDate: string
  endDate: string
  invitationToken: string
  invitationUrl: string
}

export const sendTripInvitation = async (data: InvitationEmailData) => {
  if (!process.env.RESEND_API_KEY) {
    console.log('Email would be sent to:', data.to)
    console.log('Invitation URL:', data.invitationUrl)
    return { success: false, error: 'Email service not configured' }
  }

  try {
    const emailHtml = generateInvitationEmailHTML(data)
    const emailText = generateInvitationEmailText(data)

    const result = await resend.emails.send({
      from: 'Travel Planner <<EMAIL>>', // Replace with your verified domain
      to: [data.to],
      subject: `🌍 You're invited to join "${data.tripTitle}"!`,
      html: emailHtml,
      text: emailText,
    })

    return { success: true, data: result }
  } catch (error) {
    console.error('Error sending invitation email:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

const generateInvitationEmailHTML = (data: InvitationEmailData) => {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trip Invitation</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 10px;
        }
        .trip-card {
            background: #f1f5f9;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .trip-title {
            font-size: 20px;
            font-weight: bold;
            color: #1e293b;
            margin-bottom: 10px;
        }
        .trip-details {
            color: #64748b;
            margin-bottom: 8px;
        }
        .cta-button {
            display: inline-block;
            background: #2563eb;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            color: #64748b;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🌍 Travel Planner</div>
            <h1>You're Invited to Join a Trip!</h1>
        </div>
        
        <p>Hi there!</p>
        
        <p><strong>${data.inviterName}</strong> (${data.inviterEmail}) has invited you to join their upcoming trip. They'd love to have you as part of the adventure!</p>
        
        <div class="trip-card">
            <div class="trip-title">${data.tripTitle}</div>
            ${data.tripDescription ? `<p>${data.tripDescription}</p>` : ''}
            <div class="trip-details">📍 <strong>Destination:</strong> ${data.destination}</div>
            <div class="trip-details">📅 <strong>Dates:</strong> ${data.startDate} - ${data.endDate}</div>
        </div>
        
        <p>Before the itinerary is finalized, you'll have the chance to vote on your preferences for activities, budget level, and trip pace. Your input will help create the perfect trip for everyone!</p>
        
        <div style="text-align: center;">
            <a href="${data.invitationUrl}" class="cta-button">Accept Invitation & Vote</a>
        </div>
        
        <p><strong>What happens next?</strong></p>
        <ol>
            <li>Click the button above to accept the invitation</li>
            <li>Sign up or log in to your account</li>
            <li>Vote on your trip preferences</li>
            <li>Wait for the AI-generated itinerary based on everyone's votes!</li>
        </ol>
        
        <p>If you don't want to join this trip, you can simply ignore this email.</p>
        
        <div class="footer">
            <p>This invitation will expire in 7 days.</p>
            <p>Happy travels! 🧳</p>
        </div>
    </div>
</body>
</html>
  `
}

const generateInvitationEmailText = (data: InvitationEmailData) => {
  return `
🌍 Travel Planner - Trip Invitation

Hi there!

${data.inviterName} (${data.inviterEmail}) has invited you to join their upcoming trip: "${data.tripTitle}"

Trip Details:
📍 Destination: ${data.destination}
📅 Dates: ${data.startDate} - ${data.endDate}
${data.tripDescription ? `\nDescription: ${data.tripDescription}` : ''}

Before the itinerary is finalized, you'll have the chance to vote on your preferences for activities, budget level, and trip pace. Your input will help create the perfect trip for everyone!

To accept this invitation and vote on preferences, visit:
${data.invitationUrl}

What happens next?
1. Click the link above to accept the invitation
2. Sign up or log in to your account
3. Vote on your trip preferences
4. Wait for the AI-generated itinerary based on everyone's votes!

If you don't want to join this trip, you can simply ignore this email.

This invitation will expire in 7 days.

Happy travels! 🧳

---
Travel Planner
  `
}

// Helper function to format dates for email
export const formatDateForEmail = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}
