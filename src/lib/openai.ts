import OpenAI from 'openai'

if (!process.env.OPENAI_API_KEY) {
  throw new Error('Missing OPENAI_API_KEY environment variable')
}

export const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

export const generateItinerary = async (
  destination: string,
  startDate: string,
  endDate: string,
  preferences: any,
  budget?: number
) => {
  const prompt = `
Create a detailed travel itinerary for a trip to ${destination} from ${startDate} to ${endDate}.

Trip Details:
- Destination: ${destination}
- Start Date: ${startDate}
- End Date: ${endDate}
- Budget: ${budget ? `$${budget}` : 'Not specified'}
- Preferences: ${JSON.stringify(preferences, null, 2)}

Please provide a day-by-day itinerary with the following structure:
- Each day should include 3-5 activities
- Include specific locations, times, and estimated costs
- Consider travel time between activities
- Include a mix of must-see attractions, local experiences, and dining
- Provide practical tips and recommendations

Format the response as a JSON object with this structure:
{
  "days": [
    {
      "date": "YYYY-MM-DD",
      "activities": [
        {
          "title": "Activity Name",
          "description": "Detailed description",
          "location": "Specific address or area",
          "start_time": "HH:MM",
          "end_time": "HH:MM",
          "cost": 50,
          "category": "sightseeing|dining|entertainment|transportation|accommodation",
          "booking_url": "URL if applicable",
          "notes": "Additional tips or information"
        }
      ]
    }
  ],
  "total_estimated_cost": 1500,
  "tips": [
    "General travel tips for the destination"
  ]
}
`

  try {
    const completion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: "You are a professional travel planner with extensive knowledge of destinations worldwide. Create detailed, practical, and engaging travel itineraries."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 4000,
    })

    const content = completion.choices[0]?.message?.content
    if (!content) {
      throw new Error('No content received from OpenAI')
    }

    // Parse the JSON response
    const itinerary = JSON.parse(content)
    return itinerary
  } catch (error) {
    console.error('Error generating itinerary:', error)
    throw new Error('Failed to generate itinerary')
  }
}
