'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { useAuth } from '@/hooks/useAuth'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { MapPin, Calendar, Users, CheckCircle, XCircle } from 'lucide-react'
import { formatDate } from '@/lib/utils'

interface InvitationData {
  id: string
  email: string
  status: string
  trip: {
    id: string
    title: string
    description: string
    destination: string
    start_date: string
    end_date: string
  }
  inviter: {
    full_name: string
    email: string
  }
}

export default function InvitePage({ params }: { params: Promise<{ token: string }> }) {
  const { user, loading: authLoading } = useAuth()
  const router = useRouter()
  const [invitation, setInvitation] = useState<InvitationData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [processing, setProcessing] = useState(false)
  const [token, setToken] = useState<string>('')

  useEffect(() => {
    const getToken = async () => {
      const resolvedParams = await params
      setToken(resolvedParams.token)
    }
    getToken()
  }, [params])

  useEffect(() => {
    if (token) {
      fetchInvitation()
    }
  }, [token])

  const fetchInvitation = async () => {
    try {
      const response = await fetch(`/api/invitations/${token}`)
      const data = await response.json()

      if (!response.ok) {
        setError(data.error || 'Failed to load invitation')
        return
      }

      setInvitation(data.invitation)
    } catch (err) {
      setError('Failed to load invitation')
    } finally {
      setLoading(false)
    }
  }

  const handleInvitationResponse = async (action: 'accept' | 'decline') => {
    if (!user) {
      // Redirect to login with return URL
      router.push(`/auth/login?redirectTo=/invite/${token}`)
      return
    }

    setProcessing(true)
    setError('')

    try {
      const response = await fetch(`/api/invitations/${token}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action }),
      })

      const data = await response.json()

      if (!response.ok) {
        setError(data.error || `Failed to ${action} invitation`)
        return
      }

      if (action === 'accept') {
        // Redirect to voting page
        router.push(`/trip/${invitation?.trip.id}/vote`)
      } else {
        // Show decline confirmation
        setInvitation(prev => prev ? { ...prev, status: 'declined' } : null)
      }
    } catch (err) {
      setError(`Failed to ${action} invitation`)
    } finally {
      setProcessing(false)
    }
  }

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading invitation...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
            <CardTitle>Invalid Invitation</CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <Link href="/">
              <Button>Go Home</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!invitation) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Invitation not found</p>
        </div>
      </div>
    )
  }

  if (invitation.status === 'declined') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <XCircle className="h-16 w-16 text-gray-500 mx-auto mb-4" />
            <CardTitle>Invitation Declined</CardTitle>
            <CardDescription>
              You have declined the invitation to join this trip.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <Link href="/">
              <Button>Go Home</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (invitation.status === 'accepted') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <CardTitle>Already Accepted</CardTitle>
            <CardDescription>
              You have already accepted this invitation.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <Link href={`/trip/${invitation.trip.id}/vote`}>
              <Button>Go to Voting</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        {/* Header */}
        <div className="text-center mb-8">
          <Link href="/" className="inline-flex items-center space-x-2 text-2xl font-bold text-gray-900">
            <MapPin className="h-8 w-8 text-blue-600" />
            <span>Travel Planner</span>
          </Link>
        </div>

        <Card>
          <CardHeader className="text-center">
            <Users className="h-16 w-16 text-blue-600 mx-auto mb-4" />
            <CardTitle className="text-2xl">You're Invited!</CardTitle>
            <CardDescription>
              {invitation.inviter.full_name || invitation.inviter.email} has invited you to join their trip
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Trip Details */}
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <h3 className="text-lg font-semibold">{invitation.trip.title}</h3>
              {invitation.trip.description && (
                <p className="text-gray-600">{invitation.trip.description}</p>
              )}
              <div className="flex items-center text-gray-600">
                <MapPin className="h-4 w-4 mr-2" />
                {invitation.trip.destination}
              </div>
              <div className="flex items-center text-gray-600">
                <Calendar className="h-4 w-4 mr-2" />
                {formatDate(invitation.trip.start_date)} - {formatDate(invitation.trip.end_date)}
              </div>
            </div>

            {!user && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <p className="text-blue-800 text-sm">
                  You need to sign in to accept this invitation. If you don't have an account, you can create one.
                </p>
              </div>
            )}

            {user && user.email !== invitation.email && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <p className="text-yellow-800 text-sm">
                  This invitation was sent to {invitation.email}, but you're signed in as {user.email}.
                  Please sign in with the correct account to accept this invitation.
                </p>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                onClick={() => handleInvitationResponse('accept')}
                disabled={processing || !user || user.email !== invitation.email}
                className="flex-1"
                size="lg"
              >
                {processing ? 'Processing...' : 'Accept Invitation'}
              </Button>
              <Button
                variant="outline"
                onClick={() => handleInvitationResponse('decline')}
                disabled={processing || !user || user.email !== invitation.email}
                className="flex-1"
                size="lg"
              >
                {processing ? 'Processing...' : 'Decline'}
              </Button>
            </div>

            {!user && (
              <div className="text-center space-y-4">
                <Link href={`/auth/login?redirectTo=/invite/${token}`}>
                  <Button size="lg" className="w-full">
                    Sign In to Accept
                  </Button>
                </Link>
                <div className="text-sm text-gray-600">
                  Don't have an account?{' '}
                  <Link
                    href={`/auth/signup?redirectTo=/invite/${token}`}
                    className="text-blue-600 hover:text-blue-500 font-medium"
                  >
                    Sign up
                  </Link>
                </div>
              </div>
            )}

            {error && (
              <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                {error}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
