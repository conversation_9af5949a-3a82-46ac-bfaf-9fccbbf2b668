import { NextRequest, NextResponse } from 'next/server'
import { sendTripInvitation, formatDateForEmail } from '@/lib/email'

export async function POST(request: NextRequest) {
  try {
    console.log('Test email endpoint called')
    
    const { email } = await request.json()
    
    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 })
    }

    console.log('Sending test email to:', email)

    // Send a test invitation email
    const emailResult = await sendTripInvitation({
      to: email,
      inviterName: 'Test User',
      inviterEmail: '<EMAIL>',
      tripTitle: 'Test Trip to Paris',
      tripDescription: 'A wonderful test trip to the City of Light',
      destination: 'Paris, France',
      startDate: formatDateForEmail('2025-07-01'),
      endDate: formatDateForEmail('2025-07-07'),
      invitationToken: 'test-token-123',
      invitationUrl: `${process.env.NEXT_PUBLIC_APP_URL}/invite/test-token-123`,
    })

    console.log('Email result:', emailResult)

    if (emailResult.success) {
      return NextResponse.json({ 
        success: true, 
        message: 'Test email sent successfully!',
        data: emailResult.data 
      })
    } else {
      return NextResponse.json({ 
        success: false, 
        error: emailResult.error 
      }, { status: 500 })
    }

  } catch (error) {
    console.error('Error in test email endpoint:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
