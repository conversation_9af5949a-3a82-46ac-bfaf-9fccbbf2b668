import { NextRequest, NextResponse } from 'next/server'
import { createServerComponentClient } from '@/lib/supabase-server'
import { sendTripInvitation, formatDateForEmail } from '@/lib/email'
import crypto from 'crypto'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerComponentClient()
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { tripId, emails } = body

    // Validate required fields
    if (!tripId || !emails || !Array.isArray(emails)) {
      return NextResponse.json(
        { error: 'Missing required fields: tripId, emails (array)' },
        { status: 400 }
      )
    }

    // Verify the trip belongs to the user and get user profile
    const { data: trip, error: tripError } = await supabase
      .from('trips')
      .select('*')
      .eq('id', tripId)
      .eq('user_id', user.id)
      .single()

    if (tripError || !trip) {
      return NextResponse.json({ error: 'Trip not found' }, { status: 404 })
    }

    // Get the inviter's profile
    const { data: inviterProfile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    if (profileError || !inviterProfile) {
      return NextResponse.json({ error: 'Inviter profile not found' }, { status: 404 })
    }

    // Create invitations for each email
    const invitations = []
    const errors = []

    for (const email of emails) {
      try {
        // Generate a unique token for the invitation
        const token = crypto.randomBytes(32).toString('hex')
        
        // Check if invitation already exists
        const { data: existingInvitation } = await supabase
          .from('trip_invitations')
          .select('id')
          .eq('trip_id', tripId)
          .eq('email', email.trim())
          .single()

        if (existingInvitation) {
          errors.push(`Invitation already sent to ${email}`)
          continue
        }

        // Create the invitation
        const { data: invitation, error: inviteError } = await supabase
          .from('trip_invitations')
          .insert({
            trip_id: tripId,
            inviter_id: user.id,
            email: email.trim(),
            token,
          })
          .select()
          .single()

        if (inviteError) {
          errors.push(`Failed to create invitation for ${email}: ${inviteError.message}`)
          continue
        }

        invitations.push(invitation)

        // Send email invitation
        const invitationUrl = `${process.env.NEXT_PUBLIC_APP_URL}/invite/${token}`

        const emailResult = await sendTripInvitation({
          to: email.trim(),
          inviterName: inviterProfile.full_name || inviterProfile.email,
          inviterEmail: inviterProfile.email,
          tripTitle: trip.title,
          tripDescription: trip.description,
          destination: trip.destination,
          startDate: formatDateForEmail(trip.start_date),
          endDate: formatDateForEmail(trip.end_date),
          invitationToken: token,
          invitationUrl,
        })

        if (!emailResult.success) {
          console.error(`Failed to send email to ${email}:`, emailResult.error)
          // Don't fail the entire request if email fails, just log it
        } else {
          console.log(`Invitation email sent successfully to ${email}`)
        }
        
      } catch (error) {
        errors.push(`Error processing ${email}: ${error}`)
      }
    }

    return NextResponse.json({ 
      invitations,
      errors: errors.length > 0 ? errors : undefined,
      message: `${invitations.length} invitation(s) sent successfully`
    })

  } catch (error) {
    console.error('Error in POST /api/invitations:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerComponentClient()
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const tripId = searchParams.get('tripId')

    if (!tripId) {
      return NextResponse.json({ error: 'tripId is required' }, { status: 400 })
    }

    // Verify the trip belongs to the user
    const { data: trip, error: tripError } = await supabase
      .from('trips')
      .select('*')
      .eq('id', tripId)
      .eq('user_id', user.id)
      .single()

    if (tripError || !trip) {
      return NextResponse.json({ error: 'Trip not found' }, { status: 404 })
    }

    // Get invitations for the trip
    const { data: invitations, error } = await supabase
      .from('trip_invitations')
      .select(`
        *,
        invited_user:invited_user_id(id, email, full_name)
      `)
      .eq('trip_id', tripId)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching invitations:', error)
      return NextResponse.json({ error: 'Failed to fetch invitations' }, { status: 500 })
    }

    return NextResponse.json({ invitations })
  } catch (error) {
    console.error('Error in GET /api/invitations:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
