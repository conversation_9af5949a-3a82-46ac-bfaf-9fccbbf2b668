import { NextRequest, NextResponse } from 'next/server'
import { createServerComponentClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerComponentClient()
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Fetch trips for the user
    const { data: trips, error } = await supabase
      .from('trips')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching trips:', error)
      return NextResponse.json({ error: 'Failed to fetch trips' }, { status: 500 })
    }

    return NextResponse.json({ trips })
  } catch (error) {
    console.error('Error in GET /api/trips:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerComponentClient()
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { title, description, destination, start_date, end_date, budget, preferences } = body

    // Validate required fields
    if (!title || !destination || !start_date || !end_date) {
      return NextResponse.json(
        { error: 'Missing required fields: title, destination, start_date, end_date' },
        { status: 400 }
      )
    }

    // Create the trip
    const { data: trip, error } = await supabase
      .from('trips')
      .insert({
        user_id: user.id,
        title,
        description,
        destination,
        start_date,
        end_date,
        budget,
        preferences,
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating trip:', error)
      return NextResponse.json({ error: 'Failed to create trip' }, { status: 500 })
    }

    return NextResponse.json({ trip }, { status: 201 })
  } catch (error) {
    console.error('Error in POST /api/trips:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
