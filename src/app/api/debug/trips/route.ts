import { NextRequest, NextResponse } from 'next/server'
import { createServerComponentClient } from '@/lib/supabase-server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerComponentClient()
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    console.log('Debug: Fetching trips for user:', user.id)

    // Get all trips for this user
    const { data: trips, error } = await supabase
      .from('trips')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Debug: Supabase error:', error)
      return NextResponse.json({ error: 'Failed to fetch trips', details: error }, { status: 500 })
    }

    console.log('Debug: Found trips:', trips)

    return NextResponse.json({ 
      user: { id: user.id, email: user.email },
      trips: trips || [],
      count: trips?.length || 0
    })
  } catch (error) {
    console.error('Debug: Error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
