import { NextRequest, NextResponse } from 'next/server'
import { createServerComponentClient } from '@/lib/supabase'
import { generateItinerary } from '@/lib/openai'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerComponentClient()
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { tripId, destination, startDate, endDate, preferences, budget } = body

    // Validate required fields
    if (!tripId || !destination || !startDate || !endDate) {
      return NextResponse.json(
        { error: 'Missing required fields: tripId, destination, startDate, endDate' },
        { status: 400 }
      )
    }

    // Verify the trip belongs to the user
    const { data: trip, error: tripError } = await supabase
      .from('trips')
      .select('*')
      .eq('id', tripId)
      .eq('user_id', user.id)
      .single()

    if (tripError || !trip) {
      return NextResponse.json({ error: 'Trip not found' }, { status: 404 })
    }

    // Generate itinerary using OpenAI
    const itinerary = await generateItinerary(
      destination,
      startDate,
      endDate,
      preferences,
      budget
    )

    // Update the trip with the generated itinerary
    const { error: updateError } = await supabase
      .from('trips')
      .update({ itinerary })
      .eq('id', tripId)

    if (updateError) {
      console.error('Error updating trip with itinerary:', updateError)
      return NextResponse.json({ error: 'Failed to save itinerary' }, { status: 500 })
    }

    // Create activities from the itinerary
    if (itinerary.days && Array.isArray(itinerary.days)) {
      const activities = []
      
      for (const day of itinerary.days) {
        if (day.activities && Array.isArray(day.activities)) {
          for (const activity of day.activities) {
            activities.push({
              trip_id: tripId,
              title: activity.title,
              description: activity.description,
              location: activity.location,
              date: day.date,
              start_time: activity.start_time,
              end_time: activity.end_time,
              cost: activity.cost,
              category: activity.category,
              booking_url: activity.booking_url,
              notes: activity.notes,
            })
          }
        }
      }

      if (activities.length > 0) {
        const { error: activitiesError } = await supabase
          .from('activities')
          .insert(activities)

        if (activitiesError) {
          console.error('Error creating activities:', activitiesError)
          // Don't fail the request if activities creation fails
        }
      }
    }

    return NextResponse.json({ itinerary })
  } catch (error) {
    console.error('Error in POST /api/ai/generate-itinerary:', error)
    return NextResponse.json(
      { error: 'Failed to generate itinerary' },
      { status: 500 }
    )
  }
}
