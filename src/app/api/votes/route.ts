import { NextRequest, NextResponse } from 'next/server'
import { createServerComponentClient } from '@/lib/supabase-server'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerComponentClient()
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { tripId, interests, budgetLevel, pace, additionalNotes } = body

    // Validate required fields
    if (!tripId) {
      return NextResponse.json({ error: 'tripId is required' }, { status: 400 })
    }

    // Verify the user is invited to this trip or is the trip owner
    const { data: trip, error: tripError } = await supabase
      .from('trips')
      .select('*')
      .eq('id', tripId)
      .single()

    if (tripError || !trip) {
      return NextResponse.json({ error: 'Trip not found' }, { status: 404 })
    }

    // Check if user is the trip owner or has an accepted invitation
    const isOwner = trip.user_id === user.id
    let hasInvitation = false

    if (!isOwner) {
      const { data: invitation } = await supabase
        .from('trip_invitations')
        .select('id')
        .eq('trip_id', tripId)
        .eq('email', user.email)
        .eq('status', 'accepted')
        .single()

      hasInvitation = !!invitation
    }

    if (!isOwner && !hasInvitation) {
      return NextResponse.json({ 
        error: 'You are not authorized to vote on this trip' 
      }, { status: 403 })
    }

    // Create or update the vote
    const { data: vote, error: voteError } = await supabase
      .from('trip_votes')
      .upsert({
        trip_id: tripId,
        user_id: user.id,
        interests: interests || [],
        budget_level: budgetLevel,
        pace: pace,
        additional_notes: additionalNotes,
      })
      .select()
      .single()

    if (voteError) {
      console.error('Error creating/updating vote:', voteError)
      return NextResponse.json({ error: 'Failed to save vote' }, { status: 500 })
    }

    return NextResponse.json({ vote, message: 'Vote saved successfully' })

  } catch (error) {
    console.error('Error in POST /api/votes:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerComponentClient()
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const tripId = searchParams.get('tripId')

    if (!tripId) {
      return NextResponse.json({ error: 'tripId is required' }, { status: 400 })
    }

    // Verify the user can access this trip's votes
    const { data: trip, error: tripError } = await supabase
      .from('trips')
      .select('*')
      .eq('id', tripId)
      .single()

    if (tripError || !trip) {
      return NextResponse.json({ error: 'Trip not found' }, { status: 404 })
    }

    // Check if user is the trip owner or has an accepted invitation
    const isOwner = trip.user_id === user.id
    let hasInvitation = false

    if (!isOwner) {
      const { data: invitation } = await supabase
        .from('trip_invitations')
        .select('id')
        .eq('trip_id', tripId)
        .eq('email', user.email)
        .eq('status', 'accepted')
        .single()

      hasInvitation = !!invitation
    }

    if (!isOwner && !hasInvitation) {
      return NextResponse.json({ 
        error: 'You are not authorized to view votes for this trip' 
      }, { status: 403 })
    }

    // Get all votes for the trip (without user join to avoid RLS issues)
    const { data: votes, error } = await supabase
      .from('trip_votes')
      .select('id, trip_id, user_id, interests, budget_level, pace, additional_notes, voted_at')
      .eq('trip_id', tripId)
      .order('voted_at', { ascending: false })

    if (error) {
      console.error('Error fetching votes:', error)
      return NextResponse.json({ error: 'Failed to fetch votes' }, { status: 500 })
    }

    return NextResponse.json({ votes })
  } catch (error) {
    console.error('Error in GET /api/votes:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
