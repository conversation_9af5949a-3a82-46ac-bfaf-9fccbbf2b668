import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-sans",
});

export const metadata: Metadata = {
  title: "Travel Planner - AI-Powered Itinerary Generator",
  description: "Create personalized travel itineraries with AI assistance. Plan your perfect trip with smart recommendations and detailed day-by-day schedules.",
  keywords: ["travel", "itinerary", "AI", "trip planning", "vacation", "travel planner"],
  authors: [{ name: "Travel Planner Team" }],
  openGraph: {
    title: "Travel Planner - AI-Powered Itinerary Generator",
    description: "Create personalized travel itineraries with AI assistance",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} font-sans antialiased min-h-screen bg-white`}>
        {children}
      </body>
    </html>
  );
}
