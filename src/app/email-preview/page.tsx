'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'

// This is a preview page to see how the email will look
export default function EmailPreviewPage() {
  const sampleData = {
    inviterName: '<PERSON>',
    inviterEmail: '<EMAIL>',
    tripTitle: 'Amazing Summer Adventure in Japan',
    tripDescription: 'Join us for an incredible journey through Tokyo, Kyoto, and Osaka. We\'ll explore ancient temples, modern cities, and delicious cuisine!',
    destination: 'Tokyo, Japan',
    startDate: 'Saturday, July 15, 2024',
    endDate: 'Sunday, July 28, 2024',
    invitationUrl: 'http://localhost:3000/invite/sample-token-123'
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Email Template Preview</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">
              This is how the invitation email will look when sent to invitees.
            </p>
          </CardContent>
        </Card>

        {/* Email Preview */}
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div style={{
            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, sans-serif',
            lineHeight: '1.6',
            color: '#333',
            maxWidth: '600px',
            margin: '0 auto',
            padding: '20px',
            backgroundColor: '#f8fafc'
          }}>
            <div style={{
              background: 'white',
              borderRadius: '12px',
              padding: '40px',
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
            }}>
              <div style={{ textAlign: 'center', marginBottom: '30px' }}>
                <div style={{
                  fontSize: '24px',
                  fontWeight: 'bold',
                  color: '#2563eb',
                  marginBottom: '10px'
                }}>
                  🌍 Travel Planner
                </div>
                <h1>You're Invited to Join a Trip!</h1>
              </div>
              
              <p>Hi there!</p>
              
              <p>
                <strong>{sampleData.inviterName}</strong> ({sampleData.inviterEmail}) has invited you to join their upcoming trip. They'd love to have you as part of the adventure!
              </p>
              
              <div style={{
                background: '#f1f5f9',
                borderRadius: '8px',
                padding: '20px',
                margin: '20px 0'
              }}>
                <div style={{
                  fontSize: '20px',
                  fontWeight: 'bold',
                  color: '#1e293b',
                  marginBottom: '10px'
                }}>
                  {sampleData.tripTitle}
                </div>
                <p>{sampleData.tripDescription}</p>
                <div style={{ color: '#64748b', marginBottom: '8px' }}>
                  📍 <strong>Destination:</strong> {sampleData.destination}
                </div>
                <div style={{ color: '#64748b', marginBottom: '8px' }}>
                  📅 <strong>Dates:</strong> {sampleData.startDate} - {sampleData.endDate}
                </div>
              </div>
              
              <p>Before the itinerary is finalized, you'll have the chance to vote on your preferences for activities, budget level, and trip pace. Your input will help create the perfect trip for everyone!</p>
              
              <div style={{ textAlign: 'center' }}>
                <a 
                  href={sampleData.invitationUrl}
                  style={{
                    display: 'inline-block',
                    background: '#2563eb',
                    color: 'white',
                    padding: '12px 24px',
                    textDecoration: 'none',
                    borderRadius: '6px',
                    fontWeight: '500',
                    margin: '20px 0'
                  }}
                >
                  Accept Invitation & Vote
                </a>
              </div>
              
              <p><strong>What happens next?</strong></p>
              <ol>
                <li>Click the button above to accept the invitation</li>
                <li>Sign up or log in to your account</li>
                <li>Vote on your trip preferences</li>
                <li>Wait for the AI-generated itinerary based on everyone's votes!</li>
              </ol>
              
              <p>If you don't want to join this trip, you can simply ignore this email.</p>
              
              <div style={{
                textAlign: 'center',
                marginTop: '30px',
                paddingTop: '20px',
                borderTop: '1px solid #e2e8f0',
                color: '#64748b',
                fontSize: '14px'
              }}>
                <p>This invitation will expire in 7 days.</p>
                <p>Happy travels! 🧳</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
