'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { useAuth } from '@/hooks/useAuth'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { MapPin, ArrowLeft, Calendar, DollarSign, Sparkles } from 'lucide-react'

export default function CreateTripPage() {
  const { user } = useAuth()
  const router = useRouter()
  
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    destination: '',
    start_date: '',
    end_date: '',
    budget: '',
    interests: [] as string[],
    budget_level: 'mid-range' as 'budget' | 'mid-range' | 'luxury',
    pace: 'moderate' as 'relaxed' | 'moderate' | 'packed',
  })
  
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleInterestToggle = (interest: string) => {
    setFormData(prev => ({
      ...prev,
      interests: prev.interests.includes(interest)
        ? prev.interests.filter(i => i !== interest)
        : [...prev.interests, interest]
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      // Create the trip
      const tripResponse = await fetch('/api/trips', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: formData.title,
          description: formData.description,
          destination: formData.destination,
          start_date: formData.start_date,
          end_date: formData.end_date,
          budget: formData.budget ? parseFloat(formData.budget) : null,
          preferences: {
            interests: formData.interests,
            budget_level: formData.budget_level,
            pace: formData.pace,
          },
        }),
      })

      if (!tripResponse.ok) {
        throw new Error('Failed to create trip')
      }

      const { trip } = await tripResponse.json()

      // Generate itinerary
      const itineraryResponse = await fetch('/api/ai/generate-itinerary', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          tripId: trip.id,
          destination: formData.destination,
          startDate: formData.start_date,
          endDate: formData.end_date,
          preferences: {
            interests: formData.interests,
            budget_level: formData.budget_level,
            pace: formData.pace,
          },
          budget: formData.budget ? parseFloat(formData.budget) : null,
        }),
      })

      if (!itineraryResponse.ok) {
        console.error('Failed to generate itinerary, but trip was created')
      }

      router.push(`/trip/${trip.id}`)
    } catch (err: any) {
      setError(err.message || 'An error occurred while creating your trip')
    } finally {
      setLoading(false)
    }
  }

  const interestOptions = [
    'Culture & History', 'Food & Dining', 'Adventure & Outdoor', 'Art & Museums',
    'Nightlife & Entertainment', 'Shopping', 'Nature & Wildlife', 'Architecture',
    'Photography', 'Local Experiences', 'Relaxation & Wellness', 'Sports & Activities'
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
            <div className="flex items-center space-x-2">
              <MapPin className="h-6 w-6 text-blue-600" />
              <span className="text-xl font-bold text-gray-900">Create New Trip</span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8 max-w-2xl">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Sparkles className="h-6 w-6 mr-2 text-blue-600" />
              Plan Your Adventure
            </CardTitle>
            <CardDescription>
              Tell us about your trip and we'll create a personalized itinerary for you
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {error && (
                <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                  {error}
                </div>
              )}

              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Basic Information</h3>
                
                <div className="space-y-2">
                  <label htmlFor="title" className="text-sm font-medium text-gray-700">
                    Trip Title *
                  </label>
                  <Input
                    id="title"
                    name="title"
                    placeholder="e.g., Summer Adventure in Japan"
                    value={formData.title}
                    onChange={handleInputChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="destination" className="text-sm font-medium text-gray-700">
                    Destination *
                  </label>
                  <Input
                    id="destination"
                    name="destination"
                    placeholder="e.g., Tokyo, Japan"
                    value={formData.destination}
                    onChange={handleInputChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="description" className="text-sm font-medium text-gray-700">
                    Description
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    rows={3}
                    className="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    placeholder="Tell us more about your trip..."
                    value={formData.description}
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              {/* Dates and Budget */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Dates & Budget</h3>
                
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label htmlFor="start_date" className="text-sm font-medium text-gray-700">
                      Start Date *
                    </label>
                    <Input
                      id="start_date"
                      name="start_date"
                      type="date"
                      value={formData.start_date}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label htmlFor="end_date" className="text-sm font-medium text-gray-700">
                      End Date *
                    </label>
                    <Input
                      id="end_date"
                      name="end_date"
                      type="date"
                      value={formData.end_date}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label htmlFor="budget" className="text-sm font-medium text-gray-700">
                    Budget (USD)
                  </label>
                  <Input
                    id="budget"
                    name="budget"
                    type="number"
                    placeholder="e.g., 2000"
                    value={formData.budget}
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              {/* Preferences */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Preferences</h3>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    Interests (select all that apply)
                  </label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {interestOptions.map((interest) => (
                      <button
                        key={interest}
                        type="button"
                        onClick={() => handleInterestToggle(interest)}
                        className={`text-sm px-3 py-2 rounded-md border transition-colors ${
                          formData.interests.includes(interest)
                            ? 'bg-blue-100 border-blue-300 text-blue-700'
                            : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        {interest}
                      </button>
                    ))}
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label htmlFor="budget_level" className="text-sm font-medium text-gray-700">
                      Budget Level
                    </label>
                    <select
                      id="budget_level"
                      name="budget_level"
                      value={formData.budget_level}
                      onChange={handleInputChange}
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    >
                      <option value="budget">Budget</option>
                      <option value="mid-range">Mid-range</option>
                      <option value="luxury">Luxury</option>
                    </select>
                  </div>
                  
                  <div className="space-y-2">
                    <label htmlFor="pace" className="text-sm font-medium text-gray-700">
                      Trip Pace
                    </label>
                    <select
                      id="pace"
                      name="pace"
                      value={formData.pace}
                      onChange={handleInputChange}
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    >
                      <option value="relaxed">Relaxed</option>
                      <option value="moderate">Moderate</option>
                      <option value="packed">Packed</option>
                    </select>
                  </div>
                </div>
              </div>

              <Button type="submit" className="w-full" size="lg" disabled={loading}>
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Creating Your Trip...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-5 w-5 mr-2" />
                    Create Trip & Generate Itinerary
                  </>
                )}
              </Button>
            </form>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
