'use client'

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { useAuth } from '@/hooks/useAuth'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { MapPin, ArrowLeft, Edit, Send, Calendar, DollarSign, Users, Mail } from 'lucide-react'
import { formatDate, formatCurrency, formatDateForEmail } from '@/lib/utils'

interface TripData {
  title: string
  description: string
  destination: string
  start_date: string
  end_date: string
  budget: string
  interests: string[]
  budget_level: 'budget' | 'mid-range' | 'luxury'
  pace: 'relaxed' | 'moderate' | 'packed'
  inviteEmails: string
}

export default function TripPreviewPage() {
  const { user } = useAuth()
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const [tripData, setTripData] = useState<TripData | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    // Get trip data from URL parameters
    const data: TripData = {
      title: searchParams.get('title') || '',
      description: searchParams.get('description') || '',
      destination: searchParams.get('destination') || '',
      start_date: searchParams.get('start_date') || '',
      end_date: searchParams.get('end_date') || '',
      budget: searchParams.get('budget') || '',
      interests: searchParams.get('interests')?.split(',').filter(Boolean) || [],
      budget_level: (searchParams.get('budget_level') as any) || 'mid-range',
      pace: (searchParams.get('pace') as any) || 'moderate',
      inviteEmails: searchParams.get('inviteEmails') || '',
    }

    if (!data.title || !data.destination || !data.start_date || !data.end_date) {
      router.push('/trip/create')
      return
    }

    setTripData(data)
  }, [searchParams, router])

  const handleEdit = () => {
    // Redirect back to create page with all data preserved
    const params = new URLSearchParams()
    if (tripData) {
      Object.entries(tripData).forEach(([key, value]) => {
        if (key === 'interests' && Array.isArray(value)) {
          params.set(key, value.join(','))
        } else {
          params.set(key, String(value))
        }
      })
    }
    router.push(`/trip/create?${params.toString()}`)
  }

  const handleConfirmAndSend = async () => {
    if (!tripData) return

    setLoading(true)
    setError('')

    try {
      // Create the trip
      const tripResponse = await fetch('/api/trips', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: tripData.title,
          description: tripData.description,
          destination: tripData.destination,
          start_date: tripData.start_date,
          end_date: tripData.end_date,
          budget: tripData.budget ? parseFloat(tripData.budget) : null,
          preferences: {
            interests: tripData.interests,
            budget_level: tripData.budget_level,
            pace: tripData.pace,
          },
        }),
      })

      if (!tripResponse.ok) {
        throw new Error('Failed to create trip')
      }

      const { trip } = await tripResponse.json()

      // Send invitations if emails were provided
      if (tripData.inviteEmails.trim()) {
        const emails = tripData.inviteEmails
          .split(',')
          .map(email => email.trim())
          .filter(email => email.length > 0)

        if (emails.length > 0) {
          try {
            await fetch('/api/invitations', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                tripId: trip.id,
                emails,
              }),
            })
          } catch (error) {
            console.error('Failed to send invitations:', error)
          }
        }
      }

      // Generate itinerary if no invitations
      const hasInvitations = tripData.inviteEmails.trim().length > 0
      
      if (!hasInvitations) {
        try {
          await fetch('/api/ai/generate-itinerary', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              tripId: trip.id,
              destination: tripData.destination,
              startDate: tripData.start_date,
              endDate: tripData.end_date,
              preferences: {
                interests: tripData.interests,
                budget_level: tripData.budget_level,
                pace: tripData.pace,
              },
              budget: tripData.budget ? parseFloat(tripData.budget) : null,
            }),
          })
        } catch (error) {
          console.error('Failed to generate itinerary:', error)
        }
      }

      router.push(`/trip/${trip.id}`)
    } catch (err: any) {
      setError(err.message || 'An error occurred while creating your trip')
    } finally {
      setLoading(false)
    }
  }

  if (!tripData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading preview...</p>
        </div>
      </div>
    )
  }

  const inviteEmails = tripData.inviteEmails.trim() 
    ? tripData.inviteEmails.split(',').map(email => email.trim()).filter(Boolean)
    : []

  const sampleEmailData = {
    inviterName: user?.user_metadata?.full_name || user?.email || 'You',
    inviterEmail: user?.email || '<EMAIL>',
    tripTitle: tripData.title,
    tripDescription: tripData.description,
    destination: tripData.destination,
    startDate: formatDateForEmail(tripData.start_date),
    endDate: formatDateForEmail(tripData.end_date),
    invitationUrl: `${process.env.NEXT_PUBLIC_APP_URL}/invite/sample-token-123`
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <Link href="/trip/create">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Create
              </Button>
            </Link>
            <div className="flex items-center space-x-2">
              <MapPin className="h-6 w-6 text-blue-600" />
              <span className="text-xl font-bold text-gray-900">Review Your Trip</span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Trip Summary */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="text-2xl">{tripData.title}</CardTitle>
            <CardDescription>
              Review your trip details before creating and sending invitations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-gray-700 mb-1">Destination</h4>
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 mr-2 text-gray-500" />
                    <span>{tripData.destination}</span>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold text-gray-700 mb-1">Dates</h4>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                    <span>{formatDate(tripData.start_date)} - {formatDate(tripData.end_date)}</span>
                  </div>
                </div>

                {tripData.budget && (
                  <div>
                    <h4 className="font-semibold text-gray-700 mb-1">Budget</h4>
                    <div className="flex items-center">
                      <DollarSign className="h-4 w-4 mr-2 text-gray-500" />
                      <span>{formatCurrency(parseFloat(tripData.budget))}</span>
                    </div>
                  </div>
                )}

                {tripData.description && (
                  <div>
                    <h4 className="font-semibold text-gray-700 mb-1">Description</h4>
                    <p className="text-gray-600">{tripData.description}</p>
                  </div>
                )}
              </div>

              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-gray-700 mb-2">Preferences</h4>
                  <div className="space-y-2 text-sm">
                    <p><span className="font-medium">Budget Level:</span> {tripData.budget_level}</p>
                    <p><span className="font-medium">Pace:</span> {tripData.pace}</p>
                  </div>
                </div>

                {tripData.interests.length > 0 && (
                  <div>
                    <h4 className="font-semibold text-gray-700 mb-2">Interests</h4>
                    <div className="flex flex-wrap gap-1">
                      {tripData.interests.map((interest) => (
                        <span
                          key={interest}
                          className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-md"
                        >
                          {interest}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {inviteEmails.length > 0 && (
                  <div>
                    <h4 className="font-semibold text-gray-700 mb-2">Invitations ({inviteEmails.length})</h4>
                    <div className="space-y-1">
                      {inviteEmails.map((email, index) => (
                        <div key={index} className="flex items-center text-sm">
                          <Mail className="h-3 w-3 mr-2 text-gray-500" />
                          <span>{email}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Email Preview */}
        {inviteEmails.length > 0 && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Email Preview</CardTitle>
              <CardDescription>
                This is how the invitation email will look when sent to your invitees
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Email Preview Component */}
              <div className="bg-white rounded-lg border overflow-hidden">
                <div style={{
                  fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, sans-serif',
                  lineHeight: '1.6',
                  color: '#333',
                  maxWidth: '600px',
                  margin: '0 auto',
                  padding: '20px',
                  backgroundColor: '#f8fafc'
                }}>
                  <div style={{
                    background: 'white',
                    borderRadius: '12px',
                    padding: '40px',
                    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
                  }}>
                    <div style={{ textAlign: 'center', marginBottom: '30px' }}>
                      <div style={{
                        fontSize: '24px',
                        fontWeight: 'bold',
                        color: '#2563eb',
                        marginBottom: '10px'
                      }}>
                        🌍 Travel Planner
                      </div>
                      <h1>You're Invited to Join a Trip!</h1>
                    </div>
                    
                    <p>Hi there!</p>
                    
                    <p>
                      <strong>{sampleEmailData.inviterName}</strong> ({sampleEmailData.inviterEmail}) has invited you to join their upcoming trip. They'd love to have you as part of the adventure!
                    </p>
                    
                    <div style={{
                      background: '#f1f5f9',
                      borderRadius: '8px',
                      padding: '20px',
                      margin: '20px 0'
                    }}>
                      <div style={{
                        fontSize: '20px',
                        fontWeight: 'bold',
                        color: '#1e293b',
                        marginBottom: '10px'
                      }}>
                        {sampleEmailData.tripTitle}
                      </div>
                      {sampleEmailData.tripDescription && <p>{sampleEmailData.tripDescription}</p>}
                      <div style={{ color: '#64748b', marginBottom: '8px' }}>
                        📍 <strong>Destination:</strong> {sampleEmailData.destination}
                      </div>
                      <div style={{ color: '#64748b', marginBottom: '8px' }}>
                        📅 <strong>Dates:</strong> {sampleEmailData.startDate} - {sampleEmailData.endDate}
                      </div>
                    </div>
                    
                    <p>Before the itinerary is finalized, you'll have the chance to vote on your preferences for activities, budget level, and trip pace. Your input will help create the perfect trip for everyone!</p>
                    
                    <div style={{ textAlign: 'center' }}>
                      <div style={{
                        display: 'inline-block',
                        background: '#2563eb',
                        color: 'white',
                        padding: '12px 24px',
                        textDecoration: 'none',
                        borderRadius: '6px',
                        fontWeight: '500',
                        margin: '20px 0'
                      }}>
                        Accept Invitation & Vote
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            variant="outline"
            onClick={handleEdit}
            size="lg"
            className="flex items-center"
          >
            <Edit className="h-5 w-5 mr-2" />
            Edit Trip Details
          </Button>
          
          <Button
            onClick={handleConfirmAndSend}
            disabled={loading}
            size="lg"
            className="flex items-center"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Creating Trip...
              </>
            ) : (
              <>
                <Send className="h-5 w-5 mr-2" />
                {inviteEmails.length > 0 ? 'Confirm & Send Invitations' : 'Create Trip'}
              </>
            )}
          </Button>
        </div>

        {error && (
          <div className="mt-4 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm text-center">
            {error}
          </div>
        )}

        {inviteEmails.length === 0 && (
          <div className="mt-6 text-center">
            <p className="text-gray-600 text-sm">
              No invitations will be sent. The itinerary will be generated immediately after creating the trip.
            </p>
          </div>
        )}
      </main>
    </div>
  )
}
