'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { useAuth } from '@/hooks/useAuth'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { MapPin, ArrowLeft, Calendar, DollarSign, Users, Mail, CheckCircle, Clock, X } from 'lucide-react'
import { formatDate, formatCurrency } from '@/lib/utils'

interface Trip {
  id: string
  title: string
  description: string
  destination: string
  start_date: string
  end_date: string
  budget: number
  preferences: any
  itinerary: any
  created_at: string
}

interface Invitation {
  id: string
  email: string
  status: 'pending' | 'accepted' | 'declined'
  created_at: string
  invited_user?: {
    full_name: string
  }
}

export default function TripPage({ params }: { params: Promise<{ id: string }> }) {
  const { user, loading: authLoading } = useAuth()
  const [trip, setTrip] = useState<Trip | null>(null)
  const [invitations, setInvitations] = useState<Invitation[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [tripId, setTripId] = useState<string>('')

  useEffect(() => {
    const getTripId = async () => {
      const resolvedParams = await params
      setTripId(resolvedParams.id)
    }
    getTripId()
  }, [params])

  useEffect(() => {
    if (user && tripId) {
      fetchTripData()
    }
  }, [user, tripId])

  const fetchTripData = async () => {
    try {
      // Fetch trip details
      const tripResponse = await fetch(`/api/trips/${tripId}`)
      const tripData = await tripResponse.json()

      if (!tripResponse.ok) {
        setError(tripData.error || 'Failed to load trip')
        return
      }

      setTrip(tripData.trip)

      // Fetch invitations
      const invitationsResponse = await fetch(`/api/invitations?tripId=${tripId}`)
      const invitationsData = await invitationsResponse.json()

      if (invitationsResponse.ok) {
        setInvitations(invitationsData.invitations || [])
      }

    } catch (err) {
      setError('Failed to load trip')
    } finally {
      setLoading(false)
    }
  }

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading trip...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card>
          <CardHeader>
            <CardTitle>Authentication Required</CardTitle>
            <CardDescription>Please sign in to view this trip.</CardDescription>
          </CardHeader>
          <CardContent>
            <Link href="/auth/login">
              <Button>Sign In</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle>Error</CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <Link href="/dashboard">
              <Button>Go to Dashboard</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!trip) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Trip not found</p>
        </div>
      </div>
    )
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'accepted':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'declined':
        return <X className="h-4 w-4 text-red-500" />
      default:
        return <Clock className="h-4 w-4 text-yellow-500" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'accepted':
        return 'Accepted'
      case 'declined':
        return 'Declined'
      default:
        return 'Pending'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
            <div className="flex items-center space-x-2">
              <MapPin className="h-6 w-6 text-blue-600" />
              <span className="text-xl font-bold text-gray-900">Trip Details</span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Trip Overview */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="text-2xl">{trip.title}</CardTitle>
            <CardDescription>
              {trip.description}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-4">
              <div className="flex items-center">
                <MapPin className="h-5 w-5 mr-2 text-gray-500" />
                <span>{trip.destination}</span>
              </div>
              <div className="flex items-center">
                <Calendar className="h-5 w-5 mr-2 text-gray-500" />
                <span>{formatDate(trip.start_date)} - {formatDate(trip.end_date)}</span>
              </div>
              {trip.budget && (
                <div className="flex items-center">
                  <DollarSign className="h-5 w-5 mr-2 text-gray-500" />
                  <span>{formatCurrency(trip.budget)}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Invitations */}
        {invitations.length > 0 && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2" />
                Travel Companions ({invitations.length})
              </CardTitle>
              <CardDescription>
                People you've invited to join this trip
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {invitations.map((invitation) => (
                  <div key={invitation.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Mail className="h-4 w-4 text-gray-500" />
                      <div>
                        <p className="font-medium">
                          {invitation.invited_user?.full_name || invitation.email}
                        </p>
                        {invitation.invited_user?.full_name && (
                          <p className="text-sm text-gray-500">{invitation.email}</p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(invitation.status)}
                      <span className="text-sm font-medium">
                        {getStatusText(invitation.status)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Trip Preferences */}
        {trip.preferences && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Trip Preferences</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-6">
                {trip.preferences.interests && trip.preferences.interests.length > 0 && (
                  <div>
                    <h4 className="font-semibold mb-2">Interests</h4>
                    <div className="flex flex-wrap gap-2">
                      {trip.preferences.interests.map((interest: string) => (
                        <span
                          key={interest}
                          className="px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded-md"
                        >
                          {interest}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
                <div>
                  <h4 className="font-semibold mb-2">Trip Style</h4>
                  <div className="space-y-1 text-sm">
                    <p><span className="font-medium">Budget Level:</span> {trip.preferences.budget_level}</p>
                    <p><span className="font-medium">Pace:</span> {trip.preferences.pace}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Itinerary */}
        {trip.itinerary ? (
          <Card>
            <CardHeader>
              <CardTitle>Itinerary</CardTitle>
              <CardDescription>
                AI-generated itinerary based on your preferences
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Itinerary details will be displayed here...</p>
              {/* TODO: Add itinerary display component */}
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>Itinerary</CardTitle>
              <CardDescription>
                {invitations.length > 0 
                  ? "Waiting for all invitees to vote on preferences before generating itinerary"
                  : "Ready to generate your personalized itinerary"
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              {invitations.length === 0 && (
                <Button>
                  Generate Itinerary
                </Button>
              )}
              {invitations.length > 0 && (
                <div className="text-center py-8">
                  <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">
                    Once all invited travelers have voted on their preferences, 
                    the AI will generate a personalized itinerary for your group.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </main>
    </div>
  )
}
