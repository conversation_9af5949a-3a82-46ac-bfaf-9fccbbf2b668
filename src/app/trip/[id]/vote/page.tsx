'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { useAuth } from '@/hooks/useAuth'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { MapPin, ArrowLeft, Vote, CheckCircle } from 'lucide-react'
import { formatDate } from '@/lib/utils'

interface Trip {
  id: string
  title: string
  description: string
  destination: string
  start_date: string
  end_date: string
}

export default function VotePage({ params }: { params: Promise<{ id: string }> }) {
  const { user, loading: authLoading } = useAuth()
  const router = useRouter()

  const [trip, setTrip] = useState<Trip | null>(null)
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)
  const [tripId, setTripId] = useState<string>('')
  const [tripInterests, setTripInterests] = useState<string[]>([])

  const [voteData, setVoteData] = useState({
    interests: [] as string[],
    budgetLevel: 'mid-range' as 'budget' | 'mid-range' | 'luxury',
    pace: 'moderate' as 'relaxed' | 'moderate' | 'packed',
    additionalNotes: '',
  })

  useEffect(() => {
    const getTripId = async () => {
      const resolvedParams = await params
      setTripId(resolvedParams.id)
    }
    getTripId()
  }, [params])

  useEffect(() => {
    if (user && tripId) {
      fetchTrip()
    }
  }, [user, tripId])

  const fetchTrip = async () => {
    try {
      const response = await fetch(`/api/trips/${tripId}`)
      const data = await response.json()

      if (!response.ok) {
        setError(data.error || 'Failed to load trip')
        return
      }

      setTrip(data.trip)

      // Extract interests from trip preferences
      if (data.trip.preferences && data.trip.preferences.interests) {
        setTripInterests(data.trip.preferences.interests)
      }
    } catch (err) {
      setError('Failed to load trip')
    } finally {
      setLoading(false)
    }
  }

  const handleInterestToggle = (interest: string) => {
    setVoteData(prev => ({
      ...prev,
      interests: prev.interests.includes(interest)
        ? prev.interests.filter(i => i !== interest)
        : [...prev.interests, interest]
    }))
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setVoteData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSubmitting(true)
    setError('')

    try {
      const response = await fetch('/api/votes', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          tripId: tripId,
          interests: voteData.interests,
          budgetLevel: voteData.budgetLevel,
          pace: voteData.pace,
          additionalNotes: voteData.additionalNotes,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        setError(data.error || 'Failed to submit vote')
        return
      }

      setSuccess(true)
    } catch (err) {
      setError('Failed to submit vote')
    } finally {
      setSubmitting(false)
    }
  }

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading trip...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    router.push('/auth/login')
    return null
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle>Error</CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <Link href="/dashboard">
              <Button>Go to Dashboard</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (success) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <CardTitle>Vote Submitted!</CardTitle>
            <CardDescription>
              Thank you for voting on the trip preferences. The trip organizer will use everyone's votes to create the perfect itinerary.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <Link href={`/trip/${tripId}`}>
              <Button>View Trip</Button>
            </Link>
            <Link href="/dashboard">
              <Button variant="outline" className="w-full">
                Go to Dashboard
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!trip) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Trip not found</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
            <div className="flex items-center space-x-2">
              <Vote className="h-6 w-6 text-blue-600" />
              <span className="text-xl font-bold text-gray-900">Vote on Trip Preferences</span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8 max-w-2xl">
        {/* Trip Info */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <MapPin className="h-6 w-6 mr-2 text-blue-600" />
              {trip.title}
            </CardTitle>
            <CardDescription>
              {trip.destination} • {formatDate(trip.start_date)} - {formatDate(trip.end_date)}
            </CardDescription>
          </CardHeader>
          {trip.description && (
            <CardContent>
              <p className="text-gray-600">{trip.description}</p>
            </CardContent>
          )}
        </Card>

        {/* Voting Form */}
        <Card>
          <CardHeader>
            <CardTitle>Share Your Preferences</CardTitle>
            <CardDescription>
              Help create the perfect itinerary by voting on your preferences. Your input will be combined with other travelers' votes.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Interests */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Interests</h3>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    What interests you most? (select all that apply)
                  </label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {tripInterests.map((interest) => (
                      <button
                        key={interest}
                        type="button"
                        onClick={() => handleInterestToggle(interest)}
                        className={`text-sm px-3 py-2 rounded-md border transition-colors ${
                          voteData.interests.includes(interest)
                            ? 'bg-blue-600 border-blue-600 text-white shadow-md'
                            : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400'
                        }`}
                      >
                        {interest}
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Budget Level */}
              <div className="space-y-2">
                <label htmlFor="budgetLevel" className="text-sm font-medium text-gray-700">
                  Preferred Budget Level
                </label>
                <select
                  id="budgetLevel"
                  name="budgetLevel"
                  value={voteData.budgetLevel}
                  onChange={handleInputChange}
                  className="flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
                >
                  <option value="budget">Budget-friendly</option>
                  <option value="mid-range">Mid-range</option>
                  <option value="luxury">Luxury</option>
                </select>
              </div>

              {/* Trip Pace */}
              <div className="space-y-2">
                <label htmlFor="pace" className="text-sm font-medium text-gray-700">
                  Preferred Trip Pace
                </label>
                <select
                  id="pace"
                  name="pace"
                  value={voteData.pace}
                  onChange={handleInputChange}
                  className="flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
                >
                  <option value="relaxed">Relaxed - Plenty of downtime</option>
                  <option value="moderate">Moderate - Balanced schedule</option>
                  <option value="packed">Packed - See as much as possible</option>
                </select>
              </div>

              {/* Additional Notes */}
              <div className="space-y-2">
                <label htmlFor="additionalNotes" className="text-sm font-medium text-gray-700">
                  Additional Notes (Optional)
                </label>
                <textarea
                  id="additionalNotes"
                  name="additionalNotes"
                  rows={3}
                  className="flex w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  placeholder="Any specific requests, dietary restrictions, or other preferences..."
                  value={voteData.additionalNotes}
                  onChange={handleInputChange}
                />
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                  {error}
                </div>
              )}

              <Button type="submit" className="w-full" size="lg" disabled={submitting}>
                {submitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Submitting Vote...
                  </>
                ) : (
                  <>
                    <Vote className="h-5 w-5 mr-2" />
                    Submit My Vote
                  </>
                )}
              </Button>
            </form>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
