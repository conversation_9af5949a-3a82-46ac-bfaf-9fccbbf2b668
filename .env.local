# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://placeholder.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=placeholder_anon_key
SUPABASE_SERVICE_ROLE_KEY=placeholder_service_role_key

# OpenAI Configuration
OPENAI_API_KEY=placeholder_openai_key

# Next.js Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=placeholder_secret_key_change_this

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
